'use client'

import { useState, useEffect } from 'react'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle, Clock } from 'lucide-react'
import { cn } from '@/lib/utils'

export type NotificationType = 'success' | 'error' | 'warning' | 'info' | 'loading'

export interface NotificationData {
  id: string
  type: NotificationType
  title: string
  message: string
  duration?: number
  actions?: Array<{
    label: string
    action: () => void
    variant?: 'primary' | 'secondary'
  }>
  persistent?: boolean
  progress?: number
}

interface NotificationProps {
  notification: NotificationData
  onClose: (id: string) => void
}

const NotificationComponent = ({ notification, onClose }: NotificationProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const [progress, setProgress] = useState(100)

  useEffect(() => {
    // Animate in
    setTimeout(() => setIsVisible(true), 50)

    // Auto close if not persistent
    if (!notification.persistent && notification.duration) {
      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev - (100 / (notification.duration! / 100))
          if (newProgress <= 0) {
            clearInterval(interval)
            handleClose()
            return 0
          }
          return newProgress
        })
      }, 100)

      return () => clearInterval(interval)
    }
  }, [notification.duration, notification.persistent])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(() => onClose(notification.id), 300)
  }

  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600" />
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />
      case 'info':
        return <Info className="w-5 h-5 text-blue-600" />
      case 'loading':
        return <Clock className="w-5 h-5 text-gray-600 animate-spin" />
      default:
        return <Info className="w-5 h-5 text-gray-600" />
    }
  }

  const getStyles = () => {
    const baseStyles = "border-l-4 shadow-lg"

    switch (notification.type) {
      case 'success':
        return `${baseStyles} bg-green-50 border-green-400`
      case 'error':
        return `${baseStyles} bg-red-50 border-red-400`
      case 'warning':
        return `${baseStyles} bg-yellow-50 border-yellow-400`
      case 'info':
        return `${baseStyles} bg-blue-50 border-blue-400`
      case 'loading':
        return `${baseStyles} bg-gray-50 border-gray-400`
      default:
        return `${baseStyles} bg-gray-50 border-gray-400`
    }
  }

  return (
    <div
      className={cn(
        "relative max-w-sm w-full rounded-lg p-4 transition-all duration-300 ease-in-out",
        getStyles(),
        isVisible
          ? "transform translate-x-0 opacity-100"
          : "transform translate-x-full opacity-0"
      )}
    >
      {/* Progress bar */}
      {!notification.persistent && notification.duration && (
        <div className="absolute top-0 left-0 h-1 bg-gray-200 rounded-t-lg w-full overflow-hidden">
          <div
            className="h-full bg-current transition-all duration-100 ease-linear"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}

      {/* Custom progress bar */}
      {notification.progress !== undefined && (
        <div className="absolute top-0 left-0 h-1 bg-gray-200 rounded-t-lg w-full overflow-hidden">
          <div
            className="h-full bg-blue-500 transition-all duration-300 ease-out"
            style={{ width: `${notification.progress}%` }}
          />
        </div>
      )}

      <div className="flex items-start">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>

        <div className="ml-3 flex-1">
          <h4 className="text-sm font-semibold text-gray-900">
            {notification.title}
          </h4>
          <p className="text-sm text-gray-700 mt-1">
            {notification.message}
          </p>

          {/* Actions */}
          {notification.actions && notification.actions.length > 0 && (
            <div className="mt-3 flex gap-2">
              {notification.actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.action}
                  className={cn(
                    "text-xs px-3 py-1 rounded font-medium transition-colors",
                    action.variant === 'primary'
                      ? "bg-blue-600 text-white hover:bg-blue-700"
                      : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                  )}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Close button */}
        <button
          onClick={handleClose}
          className="flex-shrink-0 ml-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

// Notification Container
interface NotificationContainerProps {
  notifications: NotificationData[]
  onClose: (id: string) => void
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

export const NotificationContainer = ({
  notifications,
  onClose,
  position = 'top-right'
}: NotificationContainerProps) => {
  const getPositionStyles = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4'
      case 'top-left':
        return 'top-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      default:
        return 'top-4 right-4'
    }
  }

  return (
    <div className={cn("fixed z-50 space-y-3", getPositionStyles())}>
      {notifications.map((notification) => (
        <NotificationComponent
          key={notification.id}
          notification={notification}
          onClose={onClose}
        />
      ))}
    </div>
  )
}

// Hook for managing notifications
export const useNotifications = () => {
  const [notifications, setNotifications] = useState<NotificationData[]>([])

  const addNotification = (notification: Omit<NotificationData, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newNotification: NotificationData = {
      id,
      duration: 5000, // Default 5 seconds
      ...notification
    }

    setNotifications(prev => [...prev, newNotification])
    return id
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const updateNotification = (id: string, updates: Partial<NotificationData>) => {
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, ...updates } : n)
    )
  }

  const clearAll = () => {
    setNotifications([])
  }

  // Convenience methods
  const success = (title: string, message: string, options?: Partial<NotificationData>) => {
    return addNotification({ type: 'success', title, message, ...options })
  }

  const error = (title: string, message: string, options?: Partial<NotificationData>) => {
    return addNotification({ type: 'error', title, message, persistent: true, ...options })
  }

  const warning = (title: string, message: string, options?: Partial<NotificationData>) => {
    return addNotification({ type: 'warning', title, message, ...options })
  }

  const info = (title: string, message: string, options?: Partial<NotificationData>) => {
    return addNotification({ type: 'info', title, message, ...options })
  }

  const loading = (title: string, message: string, options?: Partial<NotificationData>) => {
    return addNotification({ type: 'loading', title, message, persistent: true, ...options })
  }

  return {
    notifications,
    addNotification,
    removeNotification,
    updateNotification,
    clearAll,
    success,
    error,
    warning,
    info,
    loading
  }
}

// Payment specific notification helpers
export const usePaymentNotifications = () => {
  const notifications = useNotifications()

  const paymentUploadSuccess = (orderId: string) => {
    return notifications.success(
      'Bukti Pembayaran Berhasil Diupload',
      'Admin akan memverifikasi pembayaran Anda dalam 1-24 jam.',
      {
        actions: [
          {
            label: 'Lihat Pesanan',
            action: () => window.location.href = `/orders/${orderId}`,
            variant: 'primary'
          }
        ]
      }
    )
  }

  const paymentVerified = (orderId: string) => {
    return notifications.success(
      'Pembayaran Dikonfirmasi',
      'Pembayaran Anda telah diverifikasi. Pesanan sedang diproses.',
      {
        actions: [
          {
            label: 'Lihat Pesanan',
            action: () => window.location.href = `/orders/${orderId}`,
            variant: 'primary'
          }
        ]
      }
    )
  }

  const paymentRejected = (orderId: string, reason?: string) => {
    return notifications.error(
      'Pembayaran Ditolak',
      reason || 'Bukti pembayaran tidak valid. Silakan upload ulang.',
      {
        actions: [
          {
            label: 'Upload Ulang',
            action: () => window.location.href = `/payment/${orderId}`,
            variant: 'primary'
          },
          {
            label: 'Lihat Pesanan',
            action: () => window.location.href = `/orders/${orderId}`,
            variant: 'secondary'
          }
        ]
      }
    )
  }

  const orderModified = (orderId: string) => {
    return notifications.success(
      'Pesanan Berhasil Diubah',
      'Perubahan pesanan Anda telah disimpan.',
      {
        actions: [
          {
            label: 'Lihat Pesanan',
            action: () => window.location.href = `/orders/${orderId}`,
            variant: 'primary'
          }
        ]
      }
    )
  }

  return {
    ...notifications,
    paymentUploadSuccess,
    paymentVerified,
    paymentRejected,
    orderModified
  }
}
