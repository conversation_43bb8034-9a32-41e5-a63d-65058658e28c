import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const reorderSchema = z.object({
  direction: z.enum(['up', 'down'])
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { direction } = reorderSchema.parse(body)

    const currentBanner = await prisma.banner.findUnique({
      where: { id: params.id }
    })

    if (!currentBanner) {
      return NextResponse.json(
        { success: false, error: 'Banner tidak ditemukan' },
        { status: 404 }
      )
    }

    const currentOrder = currentBanner.order
    const newOrder = direction === 'up' ? currentOrder - 1 : currentOrder + 1

    // Find the banner to swap with
    const targetBanner = await prisma.banner.findFirst({
      where: {
        position: currentBanner.position,
        order: newOrder
      }
    })

    if (!targetBanner) {
      return NextResponse.json(
        { success: false, error: 'Tidak dapat mengubah urutan' },
        { status: 400 }
      )
    }

    // Swap the orders
    await prisma.$transaction([
      prisma.banner.update({
        where: { id: currentBanner.id },
        data: { order: newOrder }
      }),
      prisma.banner.update({
        where: { id: targetBanner.id },
        data: { order: currentOrder }
      })
    ])

    return NextResponse.json({
      success: true,
      message: 'Urutan banner berhasil diubah'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error('Error reordering banner:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal mengubah urutan banner' },
      { status: 500 }
    )
  }
}
