-- Enhanced Payment Flow Migration
-- Adds intermediate payment statuses and checkout recovery fields

-- Add new payment statuses to enum
ALTER TABLE `orders` MODIFY COLUMN `paymentStatus` ENUM(
  'PENDING',
  'PROOF_UPLOADED',    -- NEW: <PERSON><PERSON><PERSON> su<PERSON> diupload, menung<PERSON> verifikasi
  'UNDER_REVIEW',      -- NEW: Sedang ditinjau admin
  'PAID',
  'FAILED',
  'REFUNDED',
  'EXPIRED'
) NOT NULL DEFAULT 'PENDING';

-- Add checkout recovery and flow management fields
ALTER TABLE `orders` ADD COLUMN `checkoutData` JSON NULL COMMENT 'Stored checkout data for recovery';
ALTER TABLE `orders` ADD COLUMN `canModify` BOOLEAN DEFAULT TRUE COMMENT 'Whether order can still be modified';
ALTER TABLE `orders` ADD COLUMN `lastStatusChange` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last payment status change';
ALTER TABLE `orders` ADD COLUMN `modifiableUntil` TIMESTAMP NULL COMMENT 'Deadline for order modifications';

-- Add enhanced payment tracking fields
ALTER TABLE `payments` ADD COLUMN `uploadedAt` TIMESTAMP NULL COMMENT 'When payment proof was uploaded';
ALTER TABLE `payments` ADD COLUMN `verifiedAt` TIMESTAMP NULL COMMENT 'When payment was verified by admin';
ALTER TABLE `payments` ADD COLUMN `verifiedBy` VARCHAR(191) NULL COMMENT 'Admin who verified the payment';
ALTER TABLE `payments` ADD COLUMN `rejectionReason` TEXT NULL COMMENT 'Reason for payment rejection';

-- Add indexes for better performance
CREATE INDEX `idx_orders_payment_status` ON `orders` (`paymentStatus`);
CREATE INDEX `idx_orders_can_modify` ON `orders` (`canModify`);
CREATE INDEX `idx_orders_modifiable_until` ON `orders` (`modifiableUntil`);
CREATE INDEX `idx_payments_uploaded_at` ON `payments` (`uploadedAt`);

-- Update existing orders to set modifiable deadline (1 hour from creation)
UPDATE `orders` 
SET `modifiableUntil` = DATE_ADD(`createdAt`, INTERVAL 1 HOUR)
WHERE `paymentStatus` = 'PENDING' AND `modifiableUntil` IS NULL;

-- Update orders that have payment proof to PROOF_UPLOADED status
UPDATE `orders` o
JOIN `payments` p ON o.id = p.orderId
SET o.`paymentStatus` = 'PROOF_UPLOADED',
    o.`lastStatusChange` = NOW(),
    o.`canModify` = FALSE
WHERE o.`paymentStatus` = 'PENDING' 
  AND p.`paymentProof` IS NOT NULL 
  AND p.`paymentProof` != '';

-- Create trigger to automatically update lastStatusChange
DELIMITER $$
CREATE TRIGGER `orders_payment_status_change`
BEFORE UPDATE ON `orders`
FOR EACH ROW
BEGIN
  IF OLD.paymentStatus != NEW.paymentStatus THEN
    SET NEW.lastStatusChange = NOW();
    
    -- Auto-disable modification for certain statuses
    IF NEW.paymentStatus IN ('PAID', 'FAILED', 'EXPIRED', 'REFUNDED') THEN
      SET NEW.canModify = FALSE;
    END IF;
  END IF;
END$$
DELIMITER ;

-- Create trigger to update payment timestamps
DELIMITER $$
CREATE TRIGGER `payments_proof_upload`
BEFORE UPDATE ON `payments`
FOR EACH ROW
BEGIN
  -- Set uploadedAt when paymentProof is first added
  IF OLD.paymentProof IS NULL AND NEW.paymentProof IS NOT NULL THEN
    SET NEW.uploadedAt = NOW();
  END IF;
  
  -- Set verifiedAt when status changes to PAID
  IF OLD.status != 'PAID' AND NEW.status = 'PAID' THEN
    SET NEW.verifiedAt = NOW();
  END IF;
END$$
DELIMITER ;

-- Add comments for documentation
ALTER TABLE `orders` COMMENT = 'Enhanced orders table with payment flow management';
ALTER TABLE `payments` COMMENT = 'Enhanced payments table with verification tracking';

-- Create view for order status summary
CREATE VIEW `order_status_summary` AS
SELECT 
  o.id,
  o.orderNumber,
  o.userId,
  o.status as orderStatus,
  o.paymentStatus,
  o.paymentMethod,
  o.total,
  o.canModify,
  o.modifiableUntil,
  o.lastStatusChange,
  o.createdAt,
  p.paymentProof IS NOT NULL as hasPaymentProof,
  p.uploadedAt,
  p.verifiedAt,
  p.verifiedBy,
  CASE 
    WHEN o.paymentStatus = 'PENDING' AND p.paymentProof IS NOT NULL THEN 'PROOF_UPLOADED'
    WHEN o.paymentStatus = 'PENDING' AND o.modifiableUntil < NOW() THEN 'EXPIRED'
    ELSE o.paymentStatus
  END as effectivePaymentStatus
FROM `orders` o
LEFT JOIN `payments` p ON o.id = p.orderId;

-- Grant necessary permissions (adjust as needed)
-- GRANT SELECT, INSERT, UPDATE ON `order_status_summary` TO 'app_user'@'%';

COMMIT;
