# Acikoo Developer Guide

## 🎯 Quick Start for Developers

### Understanding the Codebase
Acikoo adalah platform e-commerce yang dibangun dengan arsitektur modern dan best practices. Berikut adalah panduan untuk developer yang ingin berkontribusi atau memahami codebase.

### Key Technologies Overview
```
Frontend: Next.js 15 + React 18 + TypeScript
Styling: Tailwind CSS + Shadcn/ui
Backend: Next.js API Routes + Prisma ORM
Database: MySQL
Auth: NextAuth.js
State: Zustand (cart) + React State
Maps: OpenStreetMap + Leaflet
Charts: Recharts
```

---

## 🏗️ Architecture Patterns

### 1. Component Architecture
```typescript
// Pattern: Compound Components
export function ProductCard({ product }: { product: Product }) {
  return (
    <Card>
      <ProductCard.Image src={product.image} />
      <ProductCard.Content>
        <ProductCard.Title>{product.name}</ProductCard.Title>
        <ProductCard.Price>{product.price}</ProductCard.Price>
      </ProductCard.Content>
      <ProductCard.Actions>
        <AddToCartButton productId={product.id} />
      </ProductCard.Actions>
    </Card>
  )
}
```

### 2. API Route Pattern
```typescript
// Pattern: Consistent API Response
export async function GET(request: NextRequest) {
  try {
    // 1. Authentication
    const session = await getServerSession(authOptions)
    
    // 2. Authorization
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }
    
    // 3. Input Validation
    const { searchParams } = new URL(request.url)
    const validatedInput = schema.parse(Object.fromEntries(searchParams))
    
    // 4. Business Logic
    const result = await businessLogic(validatedInput)
    
    // 5. Consistent Response
    return NextResponse.json({ success: true, data: result })
    
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 })
  }
}
```

### 3. State Management Pattern
```typescript
// Pattern: Zustand Store
interface CartStore {
  items: CartItem[]
  addItem: (product: Product, quantity: number) => void
  removeItem: (productId: string) => void
  updateQuantity: (productId: string, quantity: number) => void
  clearCart: () => void
  getTotalPrice: () => number
  getTotalItems: () => number
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      addItem: (product, quantity) => {
        set((state) => {
          const existingItem = state.items.find(item => item.productId === product.id)
          if (existingItem) {
            return {
              items: state.items.map(item =>
                item.productId === product.id
                  ? { ...item, quantity: item.quantity + quantity }
                  : item
              )
            }
          }
          return {
            items: [...state.items, { productId: product.id, product, quantity }]
          }
        })
      },
      // ... other methods
    }),
    { name: 'cart-storage' }
  )
)
```

---

## 🔧 Development Workflow

### 1. Feature Development Process
```bash
# 1. Create feature branch
git checkout -b feature/new-admin-feature

# 2. Install dependencies (if needed)
npm install new-dependency

# 3. Create/modify components
mkdir src/components/admin/new-feature
touch src/components/admin/new-feature/NewFeature.tsx

# 4. Add API routes (if needed)
mkdir src/app/api/admin/new-feature
touch src/app/api/admin/new-feature/route.ts

# 5. Update database schema (if needed)
# Edit prisma/schema.prisma
npx prisma db push

# 6. Test locally
npm run dev

# 7. Type check
npx tsc --noEmit

# 8. Commit and push
git add .
git commit -m "feat: add new admin feature"
git push origin feature/new-admin-feature
```

### 2. Database Schema Changes
```bash
# 1. Edit schema
# Edit prisma/schema.prisma

# 2. Generate migration (production)
npx prisma migrate dev --name add-new-feature

# 3. Or push directly (development)
npx prisma db push

# 4. Generate client
npx prisma generate

# 5. Update types (if needed)
# TypeScript types are auto-generated
```

### 3. Adding New Admin Features
```typescript
// 1. Create page component
// src/app/admin/new-feature/page.tsx
export default function NewFeaturePage() {
  return <NewFeatureComponent />
}

// 2. Add to navigation
// src/components/admin/admin-sidebar.tsx
const navigation = [
  // ... existing items
  {
    name: 'New Feature',
    href: '/admin/new-feature',
    icon: NewIcon,
  },
]

// 3. Create API endpoint
// src/app/api/admin/new-feature/route.ts
export async function GET() {
  // Implementation
}

// 4. Add to middleware (if protected)
// src/middleware.ts - usually automatic for /admin routes
```

---

## 📊 Data Flow Patterns

### 1. Customer Order Flow
```
Customer Action → Cart Store → Checkout → API Call → Database → Admin Dashboard
```

```typescript
// Example: Add to Cart Flow
const addToCart = async (productId: string, quantity: number) => {
  // 1. Get product data
  const product = await fetch(`/api/products/${productId}`).then(r => r.json())
  
  // 2. Update cart store
  useCartStore.getState().addItem(product.data, quantity)
  
  // 3. Show feedback
  toast.success('Product added to cart')
  
  // 4. Optional: Track analytics
  trackEvent('add_to_cart', { productId, quantity })
}
```

### 2. Admin Order Management Flow
```
Order Created → Admin Notification → View Details → Update Status → Customer Notification
```

```typescript
// Example: Order Status Update Flow
const updateOrderStatus = async (orderId: string, action: string) => {
  try {
    // 1. API call to update status
    const response = await fetch(`/api/admin/orders/${orderId}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action })
    })
    
    // 2. Handle response
    if (response.ok) {
      // 3. Refresh data
      await refreshOrders()
      
      // 4. Show success feedback
      toast.success('Order status updated')
      
      // 5. Optional: Send notification to customer
      await sendCustomerNotification(orderId, action)
    }
  } catch (error) {
    toast.error('Failed to update order status')
  }
}
```

---

## 🎨 UI/UX Patterns

### 1. Consistent Loading States
```typescript
// Pattern: Loading States
function DataComponent() {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  
  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }
  
  if (error) {
    return <ErrorState error={error} onRetry={fetchData} />
  }
  
  return <DataDisplay data={data} />
}
```

### 2. Form Patterns
```typescript
// Pattern: Form with Validation
const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email'),
  price: z.number().positive('Price must be positive'),
})

function ProductForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      price: 0,
    },
  })
  
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await createProduct(values)
      toast.success('Product created successfully')
      form.reset()
    } catch (error) {
      toast.error('Failed to create product')
    }
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Product Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter product name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* More fields */}
        <Button type="submit" disabled={form.formState.isSubmitting}>
          {form.formState.isSubmitting ? 'Creating...' : 'Create Product'}
        </Button>
      </form>
    </Form>
  )
}
```

### 3. Modal Patterns
```typescript
// Pattern: Reusable Modal
interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
}

function Modal({ isOpen, onClose, title, children }: ModalProps) {
  if (!isOpen) return null
  
  return (
    <>
      <div className="fixed inset-0 bg-black/50 z-40" onClick={onClose} />
      <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg p-6 max-w-md w-full">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">{title}</h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
          {children}
        </div>
      </div>
    </>
  )
}
```

---

## 🔍 Debugging & Testing

### 1. Debugging API Routes
```typescript
// Add detailed logging
export async function POST(request: NextRequest) {
  console.log('API Route called:', request.url)
  
  try {
    const body = await request.json()
    console.log('Request body:', body)
    
    const result = await processRequest(body)
    console.log('Processing result:', result)
    
    return NextResponse.json({ success: true, data: result })
  } catch (error) {
    console.error('API Error:', error)
    console.error('Error stack:', error.stack)
    return NextResponse.json({ success: false, error: error.message }, { status: 500 })
  }
}
```

### 2. Database Query Debugging
```typescript
// Enable Prisma query logging
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

// Or use Prisma Studio
// npx prisma studio
```

### 3. Component Testing
```typescript
// Example component test
import { render, screen, fireEvent } from '@testing-library/react'
import { ProductCard } from './ProductCard'

describe('ProductCard', () => {
  const mockProduct = {
    id: '1',
    name: 'Test Product',
    price: 10000,
    image: '/test-image.jpg'
  }
  
  it('renders product information', () => {
    render(<ProductCard product={mockProduct} />)
    
    expect(screen.getByText('Test Product')).toBeInTheDocument()
    expect(screen.getByText('Rp 10.000')).toBeInTheDocument()
  })
  
  it('handles add to cart click', () => {
    const mockAddToCart = jest.fn()
    render(<ProductCard product={mockProduct} onAddToCart={mockAddToCart} />)
    
    fireEvent.click(screen.getByText('Add to Cart'))
    expect(mockAddToCart).toHaveBeenCalledWith(mockProduct.id)
  })
})
```

---

## 🚀 Performance Best Practices

### 1. Component Optimization
```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }: { data: ComplexData }) => {
  const processedData = useMemo(() => {
    return expensiveCalculation(data)
  }, [data])
  
  return <div>{processedData}</div>
})

// Use useCallback for event handlers
const ParentComponent = () => {
  const [count, setCount] = useState(0)
  
  const handleClick = useCallback(() => {
    setCount(prev => prev + 1)
  }, [])
  
  return <ChildComponent onClick={handleClick} />
}
```

### 2. Database Query Optimization
```typescript
// Good: Select only needed fields
const products = await prisma.product.findMany({
  select: {
    id: true,
    name: true,
    price: true,
    // Don't select large fields like description if not needed
  },
  where: { isActive: true },
  orderBy: { createdAt: 'desc' },
  take: 20, // Limit results
})

// Good: Use includes efficiently
const orders = await prisma.order.findMany({
  include: {
    orderItems: {
      include: {
        product: {
          select: { name: true, price: true } // Only needed fields
        }
      }
    }
  }
})
```

### 3. Image Optimization
```typescript
// Use Next.js Image component
import Image from 'next/image'

<Image
  src={productImage}
  alt={productName}
  width={300}
  height={200}
  priority={isAboveFold} // For above-the-fold images
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..." // Low quality placeholder
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
/>
```

---

## 🔧 Common Development Tasks

### 1. Adding a New Product Field
```typescript
// 1. Update Prisma schema
model Product {
  // ... existing fields
  newField String?
}

// 2. Push to database
// npx prisma db push

// 3. Update TypeScript types (auto-generated)

// 4. Update forms
const productSchema = z.object({
  // ... existing fields
  newField: z.string().optional(),
})

// 5. Update UI components
function ProductForm() {
  return (
    <form>
      {/* ... existing fields */}
      <FormField name="newField" />
    </form>
  )
}
```

### 2. Adding a New Admin Page
```typescript
// 1. Create page component
// src/app/admin/new-page/page.tsx
export default function NewPage() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">New Page</h1>
      <NewPageContent />
    </div>
  )
}

// 2. Add to navigation
// src/components/admin/admin-sidebar.tsx
const navigation = [
  // ... existing items
  {
    name: 'New Page',
    href: '/admin/new-page',
    icon: NewIcon,
  },
]

// 3. Create API endpoints if needed
// src/app/api/admin/new-page/route.ts
```

### 3. Adding Real-time Features
```typescript
// Using polling for real-time updates
function useRealTimeData(endpoint: string, interval: number = 30000) {
  const [data, setData] = useState(null)
  
  useEffect(() => {
    const fetchData = async () => {
      const response = await fetch(endpoint)
      const result = await response.json()
      setData(result.data)
    }
    
    fetchData() // Initial fetch
    const intervalId = setInterval(fetchData, interval)
    
    return () => clearInterval(intervalId)
  }, [endpoint, interval])
  
  return data
}

// Usage
function Dashboard() {
  const stats = useRealTimeData('/api/admin/dashboard/stats', 30000)
  
  return <DashboardStats data={stats} />
}
```

---

## 📚 Learning Resources

### Essential Reading
- **Next.js Documentation**: https://nextjs.org/docs
- **React Documentation**: https://react.dev
- **Prisma Documentation**: https://www.prisma.io/docs
- **Tailwind CSS**: https://tailwindcss.com/docs
- **TypeScript Handbook**: https://www.typescriptlang.org/docs

### Code Examples Repository
```bash
# Clone examples
git clone https://github.com/vercel/next.js/tree/canary/examples
cd examples

# Relevant examples:
# - with-prisma
# - with-tailwindcss
# - with-typescript
# - api-routes
```

### Development Tools
- **VS Code Extensions**:
  - ES7+ React/Redux/React-Native snippets
  - Tailwind CSS IntelliSense
  - Prisma
  - TypeScript Importer
  - Auto Rename Tag
  - Bracket Pair Colorizer

- **Browser Extensions**:
  - React Developer Tools
  - Redux DevTools (if using Redux)

---

## 🤝 Contributing Guidelines

### Code Style
- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Use meaningful variable and function names
- Add JSDoc comments for complex functions
- Keep components small and focused

### Git Workflow
```bash
# Feature branch naming
feature/admin-dashboard-enhancement
feature/customer-checkout-improvement
bugfix/payment-verification-issue

# Commit message format
feat: add delivery analytics dashboard
fix: resolve QRIS payment verification bug
docs: update API documentation
refactor: optimize database queries
```

### Pull Request Process
1. Create feature branch from main
2. Implement changes with tests
3. Update documentation if needed
4. Ensure all tests pass
5. Create PR with detailed description
6. Request code review
7. Address feedback
8. Merge after approval

---

**Happy Coding! 🚀**

*This guide is maintained by the Acikoo development team and updated regularly.*
