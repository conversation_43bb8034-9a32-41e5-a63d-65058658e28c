'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { MapPin, Navigation, Clock, Truck, AlertTriangle, Search } from 'lucide-react'
import {
  loadLeafletAssets,
  STORE_LOCATION,
  DELIVERY_ZONES,
  getDeliveryInfo,
  geocodeAddress,
  getCurrentLocation,
  reverseGeocode,
  TILE_LAYERS,
  type Coordinates
} from '@/lib/leaflet-maps'
import { formatCurrency } from '@/lib/utils'

interface AddressPickerProps {
  onAddressSelect: (address: string, coordinates: Coordinates, deliveryInfo: any) => void
  initialAddress?: string
  initialCoordinates?: Coordinates
}

export function LeafletAddressPicker({ onAddressSelect, initialAddress, initialCoordinates }: AddressPickerProps) {
  const [searchInput, setSearchInput] = useState(initialAddress || '')
  const [selectedCoordinates, setSelectedCoordinates] = useState<Coordinates | null>(initialCoordinates || null)
  const [deliveryInfo, setDeliveryInfo] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const markerRef = useRef<any>(null)

  useEffect(() => {
    // Only initialize if not already initialized
    if (!mapInstanceRef.current && !isLoading) {
      initializeMap()
    }

    // Cleanup on unmount
    return () => {
      if (mapInstanceRef.current) {
        try {
          // Remove all event listeners
          mapInstanceRef.current.off()

          // Remove all layers and markers
          mapInstanceRef.current.eachLayer((layer: any) => {
            mapInstanceRef.current.removeLayer(layer)
          })

          // Remove map instance
          mapInstanceRef.current.remove()
        } catch (e) {
          console.warn('Error during map cleanup:', e)
        }
        mapInstanceRef.current = null
      }

      // Clear marker reference
      if (markerRef.current) {
        markerRef.current = null
      }
    }
  }, []) // Empty dependency array - only run once

  useEffect(() => {
    if (initialCoordinates) {
      handleLocationSelect(initialCoordinates)
    }
  }, [initialCoordinates])

  const initializeMap = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Clean up existing map instance FIRST
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.off() // Remove all event listeners
          mapInstanceRef.current.remove() // Remove map instance
        } catch (e) {
          console.warn('Error cleaning up map:', e)
        }
        mapInstanceRef.current = null
      }

      // Clear marker reference
      if (markerRef.current) {
        markerRef.current = null
      }

      // Load Leaflet assets
      await loadLeafletAssets()

      if (!mapRef.current || !window.L) {
        setError('Map container tidak tersedia')
        return
      }

      // Clear and reset the map container completely
      mapRef.current.innerHTML = ''
      // Clear Leaflet internal ID (type assertion for internal property)
      ;(mapRef.current as any)._leaflet_id = null

      // Wait a bit to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 100))

      // Check if container still exists and is visible
      const containerRect = mapRef.current.getBoundingClientRect()
      if (containerRect.width === 0 || containerRect.height === 0) {
        // Container not visible yet, wait a bit more
        await new Promise(resolve => setTimeout(resolve, 200))

        // Check again
        const newRect = mapRef.current.getBoundingClientRect()
        if (newRect.width === 0 || newRect.height === 0) {
          setError('Map container tidak terlihat. Silakan gunakan input alamat manual.')
          return
        }
      }

      // Initialize map with error handling
      const map = window.L.map(mapRef.current, {
        center: [STORE_LOCATION.lat, STORE_LOCATION.lng],
        zoom: 13,
        zoomControl: true,
        attributionControl: true,
        preferCanvas: true // Better performance
      })

      // Add tile layer (using CartoDB for better styling)
      window.L.tileLayer(TILE_LAYERS.cartodb.url, {
        attribution: TILE_LAYERS.cartodb.attribution,
        maxZoom: 19
      }).addTo(map)

      // Add store marker
      const storeIcon = window.L.divIcon({
        html: `<div class="bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold shadow-lg">🏪</div>`,
        className: 'custom-div-icon',
        iconSize: [32, 32],
        iconAnchor: [16, 16]
      })

      window.L.marker([STORE_LOCATION.lat, STORE_LOCATION.lng], { icon: storeIcon })
        .addTo(map)
        .bindPopup(`
          <div class="text-center">
            <strong>Acikoo Store</strong><br>
            <small>Jl. Tanah Merah No.15<br>Pluit, Jakarta Utara</small>
          </div>
        `)

      // Add delivery zones
      DELIVERY_ZONES.forEach((zone, index) => {
        window.L.circle([STORE_LOCATION.lat, STORE_LOCATION.lng], {
          color: zone.color,
          fillColor: zone.color,
          fillOpacity: zone.opacity,
          radius: zone.radius,
          weight: 2
        }).addTo(map).bindPopup(`
          <div class="text-center">
            <strong>Zone ${index + 1}</strong><br>
            <small>Radius: ${zone.radius / 1000}km</small><br>
            <small>Fee: ${formatCurrency(zone.fee)}</small>
          </div>
        `)
      })

      // Add click listener
      map.on('click', (e: any) => {
        const coordinates = {
          lat: e.latlng.lat,
          lng: e.latlng.lng
        }
        handleLocationSelect(coordinates)
      })

      mapInstanceRef.current = map
      setIsMapLoaded(true)

    } catch (error) {
      console.error('Failed to initialize map:', error)
      setError('Gagal memuat peta. Silakan gunakan input alamat manual.')
      setIsMapLoaded(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLocationSelect = async (coordinates: Coordinates) => {
    setSelectedCoordinates(coordinates)
    setError(null)

    // Calculate delivery info
    const info = getDeliveryInfo(coordinates)
    setDeliveryInfo(info)

    // Update map marker
    if (mapInstanceRef.current && window.L) {
      // Remove existing marker
      if (markerRef.current) {
        mapInstanceRef.current.removeLayer(markerRef.current)
      }

      // Add new marker
      const customerIcon = window.L.divIcon({
        html: `<div class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs shadow-lg">📍</div>`,
        className: 'custom-div-icon',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      })

      markerRef.current = window.L.marker([coordinates.lat, coordinates.lng], { icon: customerIcon })
        .addTo(mapInstanceRef.current)
        .bindPopup(`
          <div class="text-center">
            <strong>Lokasi Pengiriman</strong><br>
            <small>Jarak: ${info.distance}km</small><br>
            <small>Fee: ${info.isDeliverable ? formatCurrency(info.fee) : 'Tidak tersedia'}</small>
          </div>
        `)
    }

    // Try to get address from coordinates if search input is empty
    if (!searchInput.trim()) {
      try {
        const address = await reverseGeocode(coordinates)
        if (address) {
          setSearchInput(address)
        }
      } catch (error) {
        console.error('Reverse geocoding failed:', error)
      }
    }
  }

  const handleCurrentLocation = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const coordinates = await getCurrentLocation()
      handleLocationSelect(coordinates)

      if (mapInstanceRef.current) {
        mapInstanceRef.current.setView([coordinates.lat, coordinates.lng], 15)
      }
    } catch (error) {
      setError('Gagal mendapatkan lokasi. Pastikan GPS aktif dan izinkan akses lokasi.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearchAddress = async () => {
    if (!searchInput.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      const coordinates = await geocodeAddress(searchInput)

      if (coordinates) {
        handleLocationSelect(coordinates)

        if (mapInstanceRef.current) {
          mapInstanceRef.current.setView([coordinates.lat, coordinates.lng], 15)
        }
      } else {
        setError('Alamat tidak ditemukan. Silakan coba alamat yang lebih spesifik.')
      }
    } catch (error) {
      setError('Gagal mencari alamat. Silakan coba lagi.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfirmAddress = () => {
    if (selectedCoordinates && deliveryInfo && searchInput.trim()) {
      onAddressSelect(searchInput, selectedCoordinates, deliveryInfo)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="w-5 h-5 text-red-600" />
          Pilih Alamat Pengiriman
        </CardTitle>
        <p className="text-sm text-gray-600 mt-1">
          💡 Ketik alamat lengkap atau klik pada peta untuk memilih lokasi pengiriman
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Address Search */}
        <div className="space-y-2">
          <Label htmlFor="address">Alamat Lengkap</Label>
          <div className="flex gap-2">
            <Input
              id="address"
              placeholder="Masukkan alamat lengkap (contoh: Pluit, Jakarta Utara)..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleSearchAddress()
                }
              }}
              disabled={isLoading}
            />
            <Button
              onClick={handleSearchAddress}
              disabled={isLoading || !searchInput.trim()}
              size="sm"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Search className="w-4 h-4" />
              )}
            </Button>
          </div>
          {searchInput.trim() && !selectedCoordinates && (
            <p className="text-xs text-gray-500">
              Tekan Enter atau klik tombol cari untuk mencari alamat
            </p>
          )}
        </div>

        {/* Current Location Button */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleCurrentLocation}
            disabled={isLoading}
            className="flex-1"
          >
            <Navigation className="w-4 h-4 mr-2" />
            Gunakan Lokasi Saat Ini
          </Button>
        </div>

        {/* Map */}
        <div className="space-y-2">
          <div
            ref={mapRef}
            className="w-full h-64 rounded-lg border"
            style={{ minHeight: '256px' }}
          />
          {isMapLoaded && (
            <p className="text-xs text-gray-600 text-center">
              Klik pada peta untuk memilih lokasi pengiriman
            </p>
          )}
          {!isMapLoaded && (
            <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">
                  {isLoading ? 'Memuat peta...' : 'Peta tidak tersedia'}
                </p>
                <p className="text-xs text-gray-500">
                  Gunakan pencarian alamat di atas
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="w-4 h-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Delivery Info */}
        {deliveryInfo && selectedCoordinates && (
          <Card className="bg-gray-50">
            <CardContent className="p-4">
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Truck className="w-4 h-4" />
                Informasi Pengiriman
              </h4>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Jarak:</span>
                  <p className="font-medium">{deliveryInfo.distance} km</p>
                </div>

                <div>
                  <span className="text-gray-600">Zone:</span>
                  <p className="font-medium">
                    {deliveryInfo.zone > 0 ? (
                      <Badge variant="outline" className="text-xs">
                        Zone {deliveryInfo.zone}
                      </Badge>
                    ) : (
                      <span className="text-red-600">Diluar zone</span>
                    )}
                  </p>
                </div>

                <div>
                  <span className="text-gray-600">Biaya Kirim:</span>
                  <p className="font-medium">
                    {deliveryInfo.isDeliverable ? (
                      <span className="text-green-600">{formatCurrency(deliveryInfo.fee)}</span>
                    ) : (
                      <span className="text-red-600">Tidak tersedia</span>
                    )}
                  </p>
                </div>

                <div>
                  <span className="text-gray-600">Estimasi:</span>
                  <p className="font-medium flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {deliveryInfo.estimatedTime}
                  </p>
                </div>
              </div>

              <div className="mt-3">
                <Badge variant={deliveryInfo.isDeliverable ? "default" : "destructive"} className="w-full justify-center">
                  {deliveryInfo.isDeliverable ? "✅ Dapat dikirim" : "❌ Diluar jangkauan"}
                </Badge>
              </div>

              {!deliveryInfo.isDeliverable && (
                <Alert className="mt-3">
                  <AlertTriangle className="w-4 h-4" />
                  <AlertDescription>
                    Untuk saat ini layanan pengiriman di area Anda belum tersedia.
                    Silakan ambil langsung di toko atau pilih lokasi dalam radius 9km.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        )}

        {/* Confirm Button */}
        {selectedCoordinates && searchInput.trim() && (
          <Button
            onClick={handleConfirmAddress}
            className="w-full"
            disabled={!deliveryInfo?.isDeliverable}
          >
            <MapPin className="w-4 h-4 mr-2" />
            Konfirmasi Alamat Pengiriman
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

// Add global type for Leaflet
declare global {
  interface Window {
    L: any
  }
}
