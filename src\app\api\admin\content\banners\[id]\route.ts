import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateBannerSchema = z.object({
  title: z.string().min(1, 'Judul banner wajib diisi').optional(),
  subtitle: z.string().optional(),
  description: z.string().optional(),
  image: z.string().optional(),
  mobileImage: z.string().optional(),
  buttonText: z.string().optional(),
  buttonLink: z.string().optional(),
  position: z.enum(['HERO', 'PROMO', 'FOOTER']).optional(),
  isActive: z.boolean().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: bannerId } = await params
    const banner = await prisma.banner.findUnique({
      where: { id: bannerId }
    })

    if (!banner) {
      return NextResponse.json(
        { success: false, error: 'Banner tidak ditemukan' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: banner
    })
  } catch (error) {
    console.error('Error fetching banner:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal memuat banner' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: bannerId } = await params

    const body = await request.json()
    const validatedData = updateBannerSchema.parse(body)

    const existingBanner = await prisma.banner.findUnique({
      where: { id: bannerId }
    })

    if (!existingBanner) {
      return NextResponse.json(
        { success: false, error: 'Banner tidak ditemukan' },
        { status: 404 }
      )
    }

    const updateData: any = {}

    if (validatedData.title !== undefined) updateData.title = validatedData.title
    if (validatedData.subtitle !== undefined) updateData.subtitle = validatedData.subtitle
    if (validatedData.description !== undefined) updateData.description = validatedData.description
    if (validatedData.image !== undefined) updateData.image = validatedData.image
    if (validatedData.mobileImage !== undefined) updateData.mobileImage = validatedData.mobileImage
    if (validatedData.buttonText !== undefined) updateData.buttonText = validatedData.buttonText
    if (validatedData.buttonLink !== undefined) updateData.buttonLink = validatedData.buttonLink
    if (validatedData.position !== undefined) updateData.position = validatedData.position
    if (validatedData.isActive !== undefined) updateData.isActive = validatedData.isActive
    if (validatedData.startDate !== undefined) {
      updateData.startDate = validatedData.startDate ? new Date(validatedData.startDate) : null
    }
    if (validatedData.endDate !== undefined) {
      updateData.endDate = validatedData.endDate ? new Date(validatedData.endDate) : null
    }

    const banner = await prisma.banner.update({
      where: { id: bannerId },
      data: updateData
    })

    return NextResponse.json({
      success: true,
      data: banner,
      message: 'Banner berhasil diperbarui'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error('Error updating banner:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal memperbarui banner' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: bannerId } = await params
    const existingBanner = await prisma.banner.findUnique({
      where: { id: bannerId }
    })

    if (!existingBanner) {
      return NextResponse.json(
        { success: false, error: 'Banner tidak ditemukan' },
        { status: 404 }
      )
    }

    await prisma.banner.delete({
      where: { id: bannerId }
    })

    // Reorder remaining banners
    const remainingBanners = await prisma.banner.findMany({
      where: { position: existingBanner.position },
      orderBy: { order: 'asc' }
    })

    for (let i = 0; i < remainingBanners.length; i++) {
      await prisma.banner.update({
        where: { id: remainingBanners[i].id },
        data: { order: i + 1 }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Banner berhasil dihapus'
    })
  } catch (error) {
    console.error('Error deleting banner:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal menghapus banner' },
      { status: 500 }
    )
  }
}
