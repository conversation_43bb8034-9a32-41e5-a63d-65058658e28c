import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const sendMessageSchema = z.object({
  conversationId: z.string().min(1, 'Conversation ID is required'),
  content: z.string().min(1, 'Message content is required').max(1000, 'Message too long')
})

// POST /api/messages - Send new message
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const data = sendMessageSchema.parse(body)

    // Verify conversation exists and user has access
    const conversation = await prisma.conversation.findUnique({
      where: { id: data.conversationId },
      include: {
        customer: {
          select: { id: true, name: true }
        }
      }
    })

    if (!conversation) {
      return NextResponse.json(
        { success: false, error: 'Conversation not found' },
        { status: 404 }
      )
    }

    // Check access permissions
    const isAdmin = session.user.role === 'ADMIN'
    const isCustomer = conversation.customerId === session.user.id

    if (!isAdmin && !isCustomer) {
      return NextResponse.json(
        { success: false, error: 'Access denied to this conversation' },
        { status: 403 }
      )
    }

    // Create message in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the message
      const message = await tx.message.create({
        data: {
          conversationId: data.conversationId,
          senderId: session.user.id,
          content: data.content,
          isRead: false
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              role: true
            }
          }
        }
      })

      // Update conversation timestamp
      await tx.conversation.update({
        where: { id: data.conversationId },
        data: { 
          updatedAt: new Date(),
          status: 'ACTIVE'
        }
      })

      return message
    })

    // Format response
    const formattedMessage = {
      id: result.id,
      content: result.content,
      senderId: result.senderId,
      senderName: result.sender.name || (result.sender.role === 'ADMIN' ? 'Admin' : 'Customer'),
      senderRole: result.sender.role,
      createdAt: result.createdAt.toISOString(),
      isRead: result.isRead
    }

    return NextResponse.json({
      success: true,
      data: formattedMessage,
      message: 'Message sent successfully'
    })

  } catch (error) {
    console.error('Send message error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid message data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/messages - Get messages for a conversation
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const conversationId = searchParams.get('conversationId')

    if (!conversationId) {
      return NextResponse.json(
        { success: false, error: 'Conversation ID is required' },
        { status: 400 }
      )
    }

    // Verify conversation exists and user has access
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId }
    })

    if (!conversation) {
      return NextResponse.json(
        { success: false, error: 'Conversation not found' },
        { status: 404 }
      )
    }

    // Check access permissions
    const isAdmin = session.user.role === 'ADMIN'
    const isCustomer = conversation.customerId === session.user.id

    if (!isAdmin && !isCustomer) {
      return NextResponse.json(
        { success: false, error: 'Access denied to this conversation' },
        { status: 403 }
      )
    }

    // Get messages
    const messages = await prisma.message.findMany({
      where: { conversationId },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    })

    // Format messages
    const formattedMessages = messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      senderId: msg.senderId,
      senderName: msg.sender.name || (msg.sender.role === 'ADMIN' ? 'Admin' : 'Customer'),
      senderRole: msg.sender.role,
      createdAt: msg.createdAt.toISOString(),
      isRead: msg.isRead
    }))

    return NextResponse.json({
      success: true,
      data: formattedMessages
    })

  } catch (error) {
    console.error('Get messages error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
