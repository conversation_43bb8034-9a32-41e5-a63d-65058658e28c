'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Truck, MapPin, AlertCircle, CheckCircle } from 'lucide-react'
import { formatCurrency, PricingSummary } from '@/lib/delivery-pricing'

interface PricingSummaryProps {
  pricingSummary: PricingSummary
  isLocationConfirmed: boolean
  selectedLocation?: {
    zoneName: string
    distance: number
  } | null
}

export function PricingSummaryCard({ 
  pricingSummary, 
  isLocationConfirmed, 
  selectedLocation 
}: PricingSummaryProps) {
  return (
    <Card className="sticky top-4">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Truck className="w-5 h-5 mr-2 text-red-600" />
          <PERSON>kasan Pesanan
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Location Status */}
        {isLocationConfirmed && selectedLocation && (
          <div className={`p-3 rounded-lg border ${
            pricingSummary.isDeliverable 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center space-x-2">
              {pricingSummary.isDeliverable ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <AlertCircle className="w-4 h-4 text-red-600" />
              )}
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className={`font-medium text-sm ${
                    pricingSummary.isDeliverable ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {selectedLocation.zoneName}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {selectedLocation.distance.toFixed(1)} km
                  </Badge>
                </div>
                {pricingSummary.zoneDescription && (
                  <p className={`text-xs ${
                    pricingSummary.isDeliverable ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {pricingSummary.zoneDescription}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {!isLocationConfirmed && (
          <div className="p-3 rounded-lg border border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                Pilih lokasi untuk melihat ongkir
              </span>
            </div>
          </div>
        )}

        <Separator />

        {/* Pricing Breakdown */}
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span>Subtotal</span>
            <span className="font-medium">{formatCurrency(pricingSummary.subtotal)}</span>
          </div>

          <div className="flex justify-between text-sm">
            <span>Ongkos Kirim</span>
            <div className="text-right">
              {pricingSummary.isDeliverable && pricingSummary.zoneName ? (
                <div>
                  <span className="font-medium">{formatCurrency(pricingSummary.deliveryFee)}</span>
                  <div className="text-xs text-gray-500">
                    {pricingSummary.zoneName}
                  </div>
                </div>
              ) : (
                <span className="text-gray-500 text-sm">
                  {isLocationConfirmed && !pricingSummary.isDeliverable 
                    ? 'Tidak tersedia' 
                    : 'Pilih lokasi'
                  }
                </span>
              )}
            </div>
          </div>

          <Separator />

          <div className="flex justify-between font-semibold">
            <span>Total</span>
            <span className={`text-lg ${
              pricingSummary.isDeliverable || !isLocationConfirmed 
                ? 'text-red-600' 
                : 'text-gray-500'
            }`}>
              {formatCurrency(pricingSummary.total)}
            </span>
          </div>

          {/* Status Messages */}
          <div className="space-y-2">
            {!isLocationConfirmed && (
              <div className="text-center p-2 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">
                  💡 Total final akan ditampilkan setelah memilih lokasi
                </p>
              </div>
            )}
            
            {isLocationConfirmed && !pricingSummary.isDeliverable && (
              <div className="text-center p-2 bg-red-50 rounded-lg">
                <p className="text-xs text-red-700">
                  ❌ Lokasi di luar area pengiriman
                </p>
              </div>
            )}
            
            {isLocationConfirmed && pricingSummary.isDeliverable && (
              <div className="text-center p-2 bg-green-50 rounded-lg">
                <p className="text-xs text-green-700">
                  ✅ Lokasi dapat dilayani
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
