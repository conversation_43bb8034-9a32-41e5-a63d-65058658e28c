'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useSession, signOut } from 'next-auth/react'
import { User, Menu, X, ShoppingCart } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useCartStore } from '@/store/cart'
import { CartModal } from '@/components/cart/cart-modal'

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { data: session } = useSession()
  const { itemCount, setIsOpen } = useCartStore()

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' })
  }

  return (
    <>
    <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-lg overflow-hidden">
              <Image
                src="/img/logo.jpeg"
                alt="Acikoo Logo"
                width={48}
                height={48}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold text-gray-900">Acikoo</span>
              <span className="text-sm text-red-600 font-medium -mt-1">Aci Pedas Khas</span>
            </div>
          </Link>

          {/* Desktop Navigation - Centered */}
          <div className="hidden md:flex items-center space-x-8 flex-1 justify-center">
            <Link href="/" className="text-gray-700 hover:text-red-600 transition-colors font-medium">
              Beranda
            </Link>
            <Link href="/products" className="text-gray-700 hover:text-red-600 transition-colors font-medium">
              Menu
            </Link>
            <Link href="/store-location" className="text-gray-700 hover:text-red-600 transition-colors font-medium">
              Lokasi Toko
            </Link>
            <Link href="#about" className="text-gray-700 hover:text-red-600 transition-colors font-medium">
              Tentang Acikoo
            </Link>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Cart */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(true)}
              className="relative"
            >
              <ShoppingCart className="w-5 h-5" />
              {itemCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {itemCount}
                </span>
              )}
            </Button>

            {/* User Menu */}
            {session ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <User className="w-5 h-5" />
                    <span className="hidden sm:inline">{session.user?.name}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link href="/profile">Profil Saya</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/orders">Pesanan Saya</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/messages">Pesan</Link>
                  </DropdownMenuItem>
                  {session.user?.role === 'ADMIN' && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href="/admin">Dashboard Admin</Link>
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    Keluar
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/auth/signin">Masuk</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/auth/signup">Daftar</Link>
                </Button>
              </div>
            )}

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <div className="flex flex-col space-y-4">
              {/* Mobile Navigation Links */}
              <Link
                href="/"
                className="text-gray-700 hover:text-red-600 transition-colors py-2 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Beranda
              </Link>
              <Link
                href="/products"
                className="text-gray-700 hover:text-red-600 transition-colors py-2 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Menu
              </Link>
              <Link
                href="/store-location"
                className="text-gray-700 hover:text-red-600 transition-colors py-2 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Lokasi Toko
              </Link>
              <Link
                href="#about"
                className="text-gray-700 hover:text-red-600 transition-colors py-2 font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Tentang Acikoo
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>

    {/* Cart Modal */}
    <CartModal />
  </>
  )
}
