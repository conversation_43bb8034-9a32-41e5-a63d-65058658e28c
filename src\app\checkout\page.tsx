'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useCartStore } from '@/store/cart'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { MapPin, CreditCard, Truck, ArrowLeft, CheckCircle, Map } from 'lucide-react'
import { toast } from 'sonner'
import { PaymentMethod } from '@/types'
import Image from 'next/image'
import dynamic from 'next/dynamic'
import {
  getDeliveryZoneInfo,
  validateDeliveryLocation,
  createPricingSummary,
  formatCurrency
} from '@/lib/delivery-pricing'

// Dynamic import for map component (client-side only)
const DeliveryZoneMap = dynamic(
  () => import('@/components/maps/delivery-zone-map').then(mod => ({ default: mod.DeliveryZoneMap })),
  {
    ssr: false,
    loading: () => <div className="flex items-center justify-center h-64">Loading map...</div>
  }
)

// Location data interface
interface LocationData {
  address: string
  lat: number
  lng: number
  zone: number
  zoneName: string
  deliveryFee: number
  distance: number
}

// Progress indicator component
function ProgressIndicator({ currentStep }: { currentStep: number }) {
  const steps = [
    { id: 1, name: 'Keranjang', icon: '🛒' },
    { id: 2, name: 'Alamat', icon: '📍' },
    { id: 3, name: 'Pembayaran', icon: '💳' },
    { id: 4, name: 'Konfirmasi', icon: '✅' }
  ]

  return (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center">
          <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
            step.id <= currentStep
              ? 'bg-red-600 border-red-600 text-white'
              : 'bg-gray-100 border-gray-300 text-gray-400'
          }`}>
            <span className="text-sm font-medium">{step.icon}</span>
          </div>
          <span className={`ml-2 text-sm font-medium ${
            step.id <= currentStep ? 'text-red-600' : 'text-gray-400'
          }`}>
            {step.name}
          </span>
          {index < steps.length - 1 && (
            <div className={`w-8 h-0.5 mx-4 ${
              step.id < currentStep ? 'bg-red-600' : 'bg-gray-300'
            }`} />
          )}
        </div>
      ))}
    </div>
  )
}

export default function CheckoutPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const { items, total, clearCart } = useCartStore()

  const [currentStep, setCurrentStep] = useState(2) // Start at address step
  const [loading, setLoading] = useState(false)
  const [orderCompleted, setOrderCompleted] = useState(false) // Track order completion

  // Form data
  const [deliveryAddress, setDeliveryAddress] = useState('')
  const [notes, setNotes] = useState('')
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('QRIS')
  const [deliveryFee, setDeliveryFee] = useState(0) // Will be set after location selection

  // Location and map states
  const [showMap, setShowMap] = useState(false)
  const [mapKey, setMapKey] = useState(0) // Force remount key
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null)
  const [isLocationConfirmed, setIsLocationConfirmed] = useState(false)

  // Payment states
  const [paymentProof, setPaymentProof] = useState<File | null>(null)
  const [showQRIS, setShowQRIS] = useState(false)
  const [codTermsAccepted, setCodTermsAccepted] = useState(false)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (items.length === 0) {
      toast.error('Keranjang kosong')
      router.push('/')
      return
    }
  }, [session, items, router])

  // Calculate pricing summary
  const pricingSummary = createPricingSummary(
    total,
    selectedLocation?.lat,
    selectedLocation?.lng
  )

  const finalTotal = pricingSummary.total

  const handleLocationSelect = (location: LocationData) => {
    // Validate location using utility function
    const validation = validateDeliveryLocation(location.lat, location.lng)

    if (!validation.isValid) {
      toast.error(validation.message)
      return
    }

    // Update state with validated location
    setSelectedLocation(location)
    setDeliveryAddress(location.address)
    setDeliveryFee(location.deliveryFee)
    setIsLocationConfirmed(true)

    // Show success message with pricing info
    toast.success(validation.message)
  }

  const handleAddressSubmit = () => {
    if (!isLocationConfirmed || !selectedLocation) {
      toast.error('Silakan pilih lokasi pengiriman terlebih dahulu')
      return
    }

    if (selectedLocation.zone === 0) {
      toast.error('Lokasi di luar area pengiriman')
      return
    }

    setCurrentStep(3)
  }

  const handlePaymentMethodChange = (method: PaymentMethod) => {
    setPaymentMethod(method)
    // Reset payment states
    setPaymentProof(null)
    setCodTermsAccepted(false)
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Ukuran file maksimal 5MB')
        return
      }
      setPaymentProof(file)
      toast.success('Bukti pembayaran berhasil dipilih')
    }
  }

  const handleConfirmOrder = async () => {
    if (paymentMethod === 'QRIS' && !paymentProof) {
      toast.error('Silakan upload bukti pembayaran')
      return
    }

    if (paymentMethod === 'COD' && !codTermsAccepted) {
      toast.error('Silakan setujui syarat dan ketentuan COD')
      return
    }

    setLoading(true)

    try {
      // Create order
      const orderData = {
        items: items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price
        })),
        deliveryAddress,
        deliveryLat: selectedLocation?.lat,
        deliveryLng: selectedLocation?.lng,
        notes,
        paymentMethod,
        subtotal: total,
        deliveryFee,
        total: finalTotal
      }

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orderData)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Gagal membuat pesanan')
      }

      // Upload payment proof if QRIS
      if (paymentMethod === 'QRIS' && paymentProof) {
        const formData = new FormData()
        formData.append('paymentProof', paymentProof)
        formData.append('orderId', result.data.orderId)

        const uploadResponse = await fetch('/api/payments/upload-proof', {
          method: 'POST',
          body: formData
        })

        if (!uploadResponse.ok) {
          throw new Error('Gagal upload bukti pembayaran')
        }
      }

      // Mark order as completed and show success
      setOrderCompleted(true)
      toast.success('Pesanan berhasil dibuat!')

      // Clear cart silently after a short delay to prevent UI issues
      setTimeout(() => {
        clearCart(true) // Silent clear to prevent notifications
        router.push('/orders')
      }, 1000)

    } catch (error) {
      console.error('Order creation error:', error)

      // Show user-friendly error messages
      if (error instanceof Error) {
        if (error.message.includes('Insufficient stock')) {
          toast.error('Stok produk tidak mencukupi. Silakan periksa keranjang Anda.')
        } else if (error.message.includes('delivery')) {
          toast.error('Terjadi masalah dengan lokasi pengiriman. Silakan pilih ulang lokasi.')
        } else if (error.message.includes('upload')) {
          toast.error('Gagal mengupload bukti pembayaran. Silakan coba lagi.')
        } else {
          toast.error(error.message)
        }
      } else {
        toast.error('Terjadi kesalahan yang tidak terduga. Silakan coba lagi.')
      }

      // Reset order completion state on error
      setOrderCompleted(false)
    } finally {
      setLoading(false)
    }
  }

  // Handle authentication
  if (!session) {
    return null
  }

  // Handle empty cart (but allow if order is completed)
  if (items.length === 0 && !orderCompleted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Keranjang Kosong</h2>
          <p className="text-gray-600 mb-4">Silakan tambahkan produk ke keranjang terlebih dahulu</p>
          <Button onClick={() => router.push('/products')} className="bg-red-600 hover:bg-red-700">
            Mulai Belanja
          </Button>
        </div>
      </div>
    )
  }

  // Show order completion state
  if (orderCompleted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Pesanan Berhasil Dibuat!</h2>
          <p className="text-gray-600 mb-4">
            Terima kasih! Pesanan Anda sedang diproses. Anda akan diarahkan ke halaman pesanan dalam beberapa detik.
          </p>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => router.push('/')}
              className="flex-1"
            >
              Belanja Lagi
            </Button>
            <Button
              onClick={() => router.push('/orders')}
              className="flex-1 bg-red-600 hover:bg-red-700"
            >
              Lihat Pesanan
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">Checkout</h1>
        </div>

        {/* Progress Indicator */}
        <ProgressIndicator currentStep={currentStep} />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Address Section */}
            {currentStep >= 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="w-5 h-5 mr-2 text-red-600" />
                    Alamat Pengiriman
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Map Button */}
                  {currentStep === 2 && !isLocationConfirmed && (
                    <div className="text-center p-6 border-2 border-dashed border-gray-300 rounded-lg">
                      <Map className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                      <h3 className="font-medium text-gray-900 mb-2">Pilih Lokasi Pengiriman</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Gunakan peta untuk memilih lokasi dan melihat zona pengiriman
                      </p>
                      <Button
                        onClick={() => {
                          setMapKey(prev => prev + 1) // Force remount
                          setShowMap(true)
                        }}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        <Map className="w-4 h-4 mr-2" />
                        Buka Map
                      </Button>
                    </div>
                  )}

                  {/* Selected Location Display */}
                  {isLocationConfirmed && selectedLocation && (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <MapPin className="w-4 h-4 text-green-600" />
                            <span className="font-medium text-green-900">Lokasi Terpilih</span>
                            <Badge className="bg-green-600">
                              {selectedLocation.zoneName}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-700 mb-2">{selectedLocation.address}</p>
                          <div className="flex items-center space-x-4 text-sm">
                            <span className="text-gray-600">
                              Jarak: {selectedLocation.distance.toFixed(1)} km
                            </span>
                            <span className="font-semibold text-red-600">
                              Ongkir: {formatCurrency(selectedLocation.deliveryFee)}
                            </span>
                          </div>
                        </div>
                        {currentStep === 2 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setMapKey(prev => prev + 1) // Force remount
                              setShowMap(true)
                            }}
                          >
                            Ubah Lokasi
                          </Button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Manual Address Input (fallback) */}
                  {currentStep === 2 && isLocationConfirmed && (
                    <div>
                      <Label htmlFor="address">Alamat Lengkap</Label>
                      <Textarea
                        id="address"
                        placeholder="Alamat akan terisi otomatis dari peta..."
                        value={deliveryAddress}
                        onChange={(e) => setDeliveryAddress(e.target.value)}
                        className="mt-1"
                        rows={3}
                        disabled={currentStep > 2}
                      />
                    </div>
                  )}

                  <div>
                    <Label htmlFor="notes">Catatan (Opsional)</Label>
                    <Textarea
                      id="notes"
                      placeholder="Catatan untuk kurir atau penjual..."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="mt-1"
                      rows={2}
                      disabled={currentStep > 2}
                    />
                  </div>

                  {currentStep === 2 && (
                    <Button
                      onClick={handleAddressSubmit}
                      className="w-full bg-red-600 hover:bg-red-700"
                    >
                      Lanjut ke Pembayaran
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Payment Section */}
            {currentStep >= 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="w-5 h-5 mr-2 text-red-600" />
                    Metode Pembayaran
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <RadioGroup
                    value={paymentMethod}
                    onValueChange={handlePaymentMethodChange}
                    disabled={currentStep > 3}
                  >
                    <div className="flex items-center space-x-2 p-4 border rounded-lg">
                      <RadioGroupItem value="QRIS" id="qris" />
                      <Label htmlFor="qris" className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">QRIS</p>
                            <p className="text-sm text-gray-600">Bayar dengan scan QR Code</p>
                          </div>
                          <Badge variant="secondary">Instant</Badge>
                        </div>
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2 p-4 border rounded-lg">
                      <RadioGroupItem value="COD" id="cod" />
                      <Label htmlFor="cod" className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Cash on Delivery (COD)</p>
                            <p className="text-sm text-gray-600">Bayar saat barang diterima</p>
                          </div>
                          <Badge variant="outline">Manual</Badge>
                        </div>
                      </Label>
                    </div>
                  </RadioGroup>

                  {/* QRIS Payment Form */}
                  {paymentMethod === 'QRIS' && currentStep === 3 && (
                    <div className="space-y-4 p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-blue-900">Upload Bukti Pembayaran</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowQRIS(true)}
                          className="text-blue-600 border-blue-600"
                        >
                          Lihat QRIS
                        </Button>
                      </div>

                      <div>
                        <Label htmlFor="payment-proof">Bukti Transfer</Label>
                        <Input
                          id="payment-proof"
                          type="file"
                          accept="image/*"
                          onChange={handleFileUpload}
                          className="mt-1"
                        />
                        {paymentProof && (
                          <p className="text-sm text-green-600 mt-1">
                            ✓ {paymentProof.name}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* COD Terms */}
                  {paymentMethod === 'COD' && currentStep === 3 && (
                    <div className="space-y-4 p-4 bg-orange-50 rounded-lg">
                      <p className="font-medium text-orange-900">Syarat & Ketentuan COD</p>
                      <div className="text-sm text-orange-800 space-y-2">
                        <p>• Pembayaran dilakukan saat barang diterima</p>
                        <p>• Siapkan uang pas sesuai total pembayaran</p>
                        <p>• Pastikan ada yang menerima saat pengiriman</p>
                        <p>• Barang dapat diperiksa sebelum pembayaran</p>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="cod-terms"
                          checked={codTermsAccepted}
                          onChange={(e) => setCodTermsAccepted(e.target.checked)}
                          className="rounded"
                        />
                        <Label htmlFor="cod-terms" className="text-sm cursor-pointer">
                          Saya setuju dengan syarat dan ketentuan COD
                        </Label>
                      </div>
                    </div>
                  )}

                  {currentStep === 3 && (
                    <Button
                      onClick={handleConfirmOrder}
                      disabled={loading || (paymentMethod === 'QRIS' && !paymentProof) || (paymentMethod === 'COD' && !codTermsAccepted)}
                      className="w-full bg-red-600 hover:bg-red-700"
                    >
                      {loading ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>
                            {paymentMethod === 'QRIS' && paymentProof
                              ? 'Mengupload bukti pembayaran...'
                              : 'Memproses pesanan...'
                            }
                          </span>
                        </div>
                      ) : (
                        'Konfirmasi Pesanan'
                      )}
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Truck className="w-5 h-5 mr-2 text-red-600" />
                  Ringkasan Pesanan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Items */}
                <div className="space-y-3">
                  {items.map((item) => (
                    <div key={item.productId} className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                        <Image
                          src={item.image || '/img/default-product.jpg'}
                          alt={item.name}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm truncate">{item.name}</p>
                        <p className="text-xs text-gray-600">
                          {item.quantity}x {formatCurrency(item.price)}
                        </p>
                      </div>
                      <p className="font-medium text-sm">
                        {formatCurrency(item.price * item.quantity)}
                      </p>
                    </div>
                  ))}
                </div>

                <Separator />

                {/* Pricing */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>{formatCurrency(pricingSummary.subtotal)}</span>
                  </div>

                  {/* Show delivery fee based on pricing summary */}
                  {pricingSummary.isDeliverable && pricingSummary.zoneName ? (
                    <div className="flex justify-between text-sm">
                      <span>Ongkir ({pricingSummary.zoneName})</span>
                      <span>{formatCurrency(pricingSummary.deliveryFee)}</span>
                    </div>
                  ) : (
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>Ongkos Kirim</span>
                      <span>
                        {isLocationConfirmed && !pricingSummary.isDeliverable
                          ? 'Tidak tersedia'
                          : 'Pilih lokasi dulu'
                        }
                      </span>
                    </div>
                  )}

                  <Separator />

                  {/* Total with conditional styling */}
                  <div className="flex justify-between font-bold">
                    <span>Total</span>
                    <span className={`${
                      pricingSummary.isDeliverable || !isLocationConfirmed
                        ? 'text-red-600'
                        : 'text-gray-500'
                    }`}>
                      {formatCurrency(pricingSummary.total)}
                    </span>
                  </div>

                  {/* Informational messages */}
                  {!isLocationConfirmed && (
                    <p className="text-xs text-gray-500 text-center">
                      *Total final akan ditampilkan setelah memilih lokasi
                    </p>
                  )}

                  {isLocationConfirmed && !pricingSummary.isDeliverable && (
                    <p className="text-xs text-red-500 text-center">
                      *Lokasi di luar area pengiriman
                    </p>
                  )}

                  {isLocationConfirmed && pricingSummary.isDeliverable && pricingSummary.zoneDescription && (
                    <p className="text-xs text-green-600 text-center">
                      ✓ {pricingSummary.zoneDescription}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* QRIS Modal */}
        {showQRIS && (
          <>
            <div
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setShowQRIS(false)}
            />
            <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-lg p-6 max-w-sm w-full">
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-4">Scan QRIS untuk Pembayaran</h3>
                  <div className="w-64 h-64 mx-auto mb-4 border rounded-lg overflow-hidden">
                    <Image
                      src="/img/QRIS.png"
                      alt="QRIS Code"
                      width={256}
                      height={256}
                      className="w-full h-full object-contain"
                    />
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    Total: <span className="font-bold text-red-600">{formatCurrency(finalTotal)}</span>
                  </p>
                  <Button
                    onClick={() => setShowQRIS(false)}
                    className="w-full"
                  >
                    Tutup
                  </Button>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Delivery Zone Map */}
        {showMap && (
          <Suspense fallback={<div>Loading map...</div>}>
            <DeliveryZoneMap
              key={`delivery-map-${mapKey}`} // Force remount dengan mapKey
              onLocationSelect={handleLocationSelect}
              onClose={() => setShowMap(false)}
              initialAddress={deliveryAddress}
            />
          </Suspense>
        )}

        {/* Loading Overlay */}
        {loading && (
          <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
            <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
              <div className="text-center">
                <div className="w-12 h-12 border-4 border-red-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {paymentMethod === 'QRIS' && paymentProof
                    ? 'Mengupload Bukti Pembayaran'
                    : 'Memproses Pesanan'
                  }
                </h3>
                <p className="text-sm text-gray-600">
                  {paymentMethod === 'QRIS' && paymentProof
                    ? 'Sedang mengupload bukti pembayaran Anda...'
                    : 'Sedang membuat pesanan Anda...'
                  }
                </p>
                <p className="text-xs text-gray-500 mt-2">
                  Mohon tunggu dan jangan tutup halaman ini
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
