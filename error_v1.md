Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/dashboard/stats?timeRange=7d 500 in 1590ms
Get notifications error: Error [PrismaClientValidationError]:
Invalid `prisma.order.findMany()` invocation:

{
  where: {
    status: "PENDING"
            ~~~~~~~~~
  },
  include: {
    user: {
      select: {
        name: true,
        email: true
      }
    }
  },
  orderBy: {
    createdAt: "desc"
  },
  take: 5
}

Invalid value for argument `status`. Expected OrderStatus.
    at async GET (src\app\api\admin\notifications\route.ts:27:8)
  25 |       lowStockProducts,
  26 |       recentOrders
> 27 |     ] = await Promise.all([
     |        ^
  28 |       // Pending orders
  29 |       prisma.order.findMany({
  30 |         where: { status: 'PENDING' }, {
  clientVersion: '6.8.2'
}
 GET /api/admin/notifications?limit=10 500 in 1707ms
Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/dashboard/stats?timeRange=24h 500 in 1822ms
 GET /api/admin/dashboard/recent-orders?limit=5 200 in 1817ms
 GET /api/admin/dashboard/product-sales?timeRange=7d 200 in 1830ms
Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/dashboard/stats?timeRange=7d 500 in 116ms
Get notifications error: Error [PrismaClientValidationError]:
Invalid `prisma.order.findMany()` invocation:

{
  where: {
    status: "PENDING"
            ~~~~~~~~~
  },
  include: {
    user: {
      select: {
        name: true,
        email: true
      }
    }
  },
  orderBy: {
    createdAt: "desc"
  },
  take: 5
}

Invalid value for argument `status`. Expected OrderStatus.
    at async GET (src\app\api\admin\notifications\route.ts:27:8)
  25 |       lowStockProducts,
  26 |       recentOrders
> 27 |     ] = await Promise.all([
     |        ^
  28 |       // Pending orders
  29 |       prisma.order.findMany({
  30 |         where: { status: 'PENDING' }, {
  clientVersion: '6.8.2'
}
 GET /api/admin/notifications?limit=10 500 in 205ms
 GET /api/admin/dashboard/recent-orders?limit=5 200 in 207ms
Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/dashboard/stats?timeRange=24h 500 in 274ms
 GET /api/admin/dashboard/product-sales?timeRange=7d 200 in 279ms
Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/dashboard/stats?timeRange=24h 500 in 94ms
Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/dashboard/stats?timeRange=24h 500 in 73ms
 ○ Compiling /admin/products ...
 ✓ Compiled /admin/products in 1047ms (2571 modules)
 GET /admin/products 200 in 1124ms
 ○ Compiling /api/categories ...
 ✓ Compiled /api/categories in 956ms (2575 modules)
 GET /api/categories 200 in 1053ms
 GET /api/categories 200 in 13ms
 GET /api/admin/products?page=1&limit=20 200 in 1075ms
 GET /api/admin/products?page=1&limit=20 200 in 21ms
 ○ Compiling /admin/products/new ...
 ✓ Compiled /admin/products/new in 582ms (2592 modules)
 GET /admin/products/new 200 in 640ms
 GET /api/categories 200 in 65ms
 GET /api/categories 200 in 19ms
 ✓ Compiled in 409ms (1091 modules)
 GET /admin/products/new 200 in 67ms
Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/dashboard/stats?timeRange=24h 500 in 90ms
Get notifications error: Error [PrismaClientValidationError]:
Invalid `prisma.order.findMany()` invocation:

{
  where: {
    status: "PENDING"
            ~~~~~~~~~
  },
  include: {
    user: {
      select: {
        name: true,
        email: true
      }
    }
  },
  orderBy: {
    createdAt: "desc"
  },
  take: 5
}

Invalid value for argument `status`. Expected OrderStatus.
    at async GET (src\app\api\admin\notifications\route.ts:27:8)
  25 |       lowStockProducts,
  26 |       recentOrders
> 27 |     ] = await Promise.all([
     |        ^
  28 |       // Pending orders
  29 |       prisma.order.findMany({
  30 |         where: { status: 'PENDING' }, {
  clientVersion: '6.8.2'
}
 GET /api/admin/notifications?limit=10 500 in 154ms
Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/dashboard/stats?timeRange=24h 500 in 124ms
 ○ Compiling /admin/categories ...
 ✓ Compiled /admin/categories in 615ms (2603 modules)
 GET /admin/categories 200 in 680ms
 ○ Compiling /api/admin/categories ...
 ✓ Compiled /api/admin/categories in 552ms (2605 modules)
 GET /api/admin/categories 200 in 626ms
 GET /api/admin/categories 200 in 20ms
 ✓ Compiled /_not-found in 445ms (2609 modules)
 GET /categories/menu-utama.jpg 404 in 574ms
 ⨯ The requested resource isn't a valid image for /categories/menu-utama.jpg received text/html; charset=utf-8
 GET /categories/menu-spesial.jpg 404 in 576ms
 ⨯ The requested resource isn't a valid image for /categories/menu-spesial.jpg received text/html; charset=utf-8
 ○ Compiling /admin/orders ...
 ✓ Compiled /admin/orders in 534ms (2563 modules)
 GET /admin/orders 200 in 671ms
 ○ Compiling /api/admin/customers ...
 ✓ Compiled /api/admin/customers in 1037ms (2569 modules)
Get admin customers error: Error [PrismaClientValidationError]:
Invalid `prisma.user.findMany()` invocation:

{
  where: {
    role: "CUSTOMER"
  },
  include: {
    _count: {
      select: {
        orders: true
      }
    },
    orders: {
      select: {
        total: true,
        createdAt: true,
        paymentStatus: true,
        ~~~~~~~~~~~~~
?       id?: true,
?       orderNumber?: true,
?       userId?: true,
?       status?: true,
?       paymentMethod?: true,
?       subtotal?: true,
?       deliveryFee?: true,
?       deliveryAddress?: true,
?       deliveryLat?: true,
?       deliveryLng?: true,
?       notes?: true,
?       updatedAt?: true,
?       user?: true,
?       orderItems?: true,
?       payment?: true,
?       _count?: true
      },
      orderBy: {
        createdAt: "desc"
      }
    }
  },
  orderBy: {
    createdAt: "desc"
  }
}

Unknown field `paymentStatus` for select statement on model `Order`. Available options are marked with ?.
    at async GET (src\app\api\admin\customers\route.ts:52:34)
  50 |
  51 |     // Get customers with order aggregations - simplified query
> 52 |     const [allCustomers, total] = await Promise.all([
     |                                  ^
  53 |       prisma.user.findMany({
  54 |         where,
  55 |         include: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/customers 500 in 1180ms
 GET /api/admin/orders? 200 in 1184ms
Get admin customers error: Error [PrismaClientValidationError]:
Invalid `prisma.user.findMany()` invocation:

{
  where: {
    role: "CUSTOMER"
  },
  include: {
    _count: {
      select: {
        orders: true
      }
    },
    orders: {
      select: {
        total: true,
        createdAt: true,
        paymentStatus: true,
        ~~~~~~~~~~~~~
?       id?: true,
?       orderNumber?: true,
?       userId?: true,
?       status?: true,
?       paymentMethod?: true,
?       subtotal?: true,
?       deliveryFee?: true,
?       deliveryAddress?: true,
?       deliveryLat?: true,
?       deliveryLng?: true,
?       notes?: true,
?       updatedAt?: true,
?       user?: true,
?       orderItems?: true,
?       payment?: true,
?       _count?: true
      },
      orderBy: {
        createdAt: "desc"
      }
    }
  },
  orderBy: {
    createdAt: "desc"
  }
}

Unknown field `paymentStatus` for select statement on model `Order`. Available options are marked with ?.
    at async GET (src\app\api\admin\customers\route.ts:52:34)
  50 |
  51 |     // Get customers with order aggregations - simplified query
> 52 |     const [allCustomers, total] = await Promise.all([
     |                                  ^
  53 |       prisma.user.findMany({
  54 |         where,
  55 |         include: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/customers 500 in 101ms
 GET /api/admin/orders? 200 in 109ms
 ○ Compiling /admin/delivery-analytics ...
 ✓ Compiled /admin/delivery-analytics in 535ms (2580 modules)
 GET /admin/delivery-analytics 200 in 621ms
 ○ Compiling /api/admin/delivery/analytics ...
 ✓ Compiled /api/admin/delivery/analytics in 700ms (2582 modules)
 GET /api/admin/delivery/analytics?timeRange=30d 200 in 778ms
 GET /api/admin/delivery/analytics?timeRange=30d 200 in 18ms
Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
 GET /api/admin/dashboard/stats?timeRange=24h 500 in 118ms
Get notifications error: Error [PrismaClientValidationError]:
Invalid `prisma.order.findMany()` invocation:

{
  where: {
    status: "PENDING"
            ~~~~~~~~~
  },
  include: {
    user: {
      select: {
        name: true,
        email: true
      }
    }
  },
  orderBy: {
    createdAt: "desc"
  },
  take: 5
}

Invalid value for argument `status`. Expected OrderStatus.
    at async GET (src\app\api\admin\notifications\route.ts:27:8)
  25 |       lowStockProducts,
  26 |       recentOrders
> 27 |     ] = await Promise.all([
     |        ^
  28 |       // Pending orders
  29 |       prisma.order.findMany({
  30 |         where: { status: 'PENDING' }, {
  clientVersion: '6.8.2'
}
 GET /api/admin/notifications?limit=10 500 in 178ms
Get dashboard stats error: Error [PrismaClientValidationError]:
Invalid `prisma.message.count()` invocation:

{
  select: {
    _count: {
      select: {
        _all: true
      }
    }
  },
  where: {
    isRead: false,
    user: {
    ~~~~
      role: "CUSTOMER"
    },
?   AND?: MessageWhereInput | MessageWhereInput[],
?   OR?: MessageWhereInput[],
?   NOT?: MessageWhereInput | MessageWhereInput[],
?   id?: StringFilter | String,
?   conversationId?: StringFilter | String,
?   senderId?: StringFilter | String,
?   content?: StringFilter | String,
?   createdAt?: DateTimeFilter | DateTime,
?   updatedAt?: DateTimeFilter | DateTime,
?   conversation?: ConversationScalarRelationFilter | ConversationWhereInput,
?   sender?: UserScalarRelationFilter | UserWhereInput
  }
}

Unknown argument `user`. Available options are marked with ?.
    at async GET (src\app\api\admin\dashboard\stats\route.ts:57:8)
  55 |       totalRevenue,
  56 |       previousRevenue
> 57 |     ] = await Promise.all([
     |        ^
  58 |       // Current period customers (new registrations)
  59 |       prisma.user.count({
  60 |         where: { {
  clientVersion: '6.8.2'
}
