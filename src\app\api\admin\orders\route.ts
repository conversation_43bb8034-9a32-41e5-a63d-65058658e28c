import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { subDays, startOfDay, endOfDay } from 'date-fns'

// GET /api/admin/orders - Get all orders for admin
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50') // Increase default limit
    const status = searchParams.get('status')
    const paymentMethod = searchParams.get('paymentMethod')
    const customerId = searchParams.get('customerId')
    const dateRange = searchParams.get('dateRange')
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    if (status) where.status = status
    if (paymentMethod) where.paymentMethod = paymentMethod
    if (customerId) where.userId = customerId

    // Date filtering
    if (dateRange && dateRange !== 'all') {
      const now = new Date()
      let startDate: Date

      switch (dateRange) {
        case 'today':
          startDate = startOfDay(now)
          where.createdAt = {
            gte: startDate,
            lte: endOfDay(now)
          }
          break
        case 'week':
          startDate = startOfDay(subDays(now, 7))
          where.createdAt = { gte: startDate }
          break
        case 'month':
          startDate = startOfDay(subDays(now, 30))
          where.createdAt = { gte: startDate }
          break
        case '3months':
          startDate = startOfDay(subDays(now, 90))
          where.createdAt = { gte: startDate }
          break
      }
    }

    const orders = await prisma.order.findMany({
      where,
      include: {
        orderItems: {
          include: {
            product: true
          }
        },
        payment: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    })

    const total = await prisma.order.count({ where })

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get admin orders error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
