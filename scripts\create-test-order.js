const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createTestOrder() {
  try {
    console.log('🚀 Creating test order with SHIPPED status...')

    // Find customer user - try both emails
    let customer = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!customer) {
      customer = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      })
    }

    if (!customer) {
      console.log('❌ Customer not found. Please run seed first.')
      return
    }

    // Find a product
    const product = await prisma.product.findFirst()

    if (!product) {
      console.log('❌ No products found. Please run seed first.')
      return
    }

    // Generate order number
    const orderNumber = `ORD-${Date.now()}`

    // Calculate totals
    const subtotal = Number(product.price)
    const deliveryFee = 8000
    const total = subtotal + deliveryFee

    console.log(`💰 Product price: ${subtotal}`)
    console.log(`🚚 Delivery fee: ${deliveryFee}`)
    console.log(`💰 Total: ${total}`)

    // Create order with SHIPPED status and delivery coordinates
    const order = await prisma.order.create({
      data: {
        orderNumber,
        userId: customer.id,
        status: 'SHIPPED',
        paymentMethod: 'QRIS',
        subtotal: subtotal,
        deliveryFee: deliveryFee,
        total: total,
        deliveryAddress: 'Kelapa Gading Mall, Jl. Boulevard Raya, Kelapa Gading, Jakarta Utara',
        deliveryLat: -6.1478, // Kelapa Gading coordinates
        deliveryLng: 106.8917,
        notes: 'Test order untuk testing peta pengiriman',
        orderItems: {
          create: {
            productId: product.id,
            quantity: 1,
            price: subtotal,
            subtotal: subtotal
          }
        },
        payment: {
          create: {
            method: 'QRIS',
            status: 'VERIFIED',
            amount: total,
            verifiedAt: new Date()
          }
        }
      },
      include: {
        orderItems: {
          include: {
            product: true
          }
        },
        payment: true
      }
    })

    console.log('✅ Test order created successfully!')
    console.log(`📦 Order Number: ${order.orderNumber}`)
    console.log(`📍 Status: ${order.status}`)
    console.log(`🗺️ Delivery Location: ${order.deliveryAddress}`)
    console.log(`📍 Coordinates: ${order.deliveryLat}, ${order.deliveryLng}`)
    console.log(`💰 Total: Rp ${order.total}`)

  } catch (error) {
    console.error('❌ Error creating test order:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestOrder()
