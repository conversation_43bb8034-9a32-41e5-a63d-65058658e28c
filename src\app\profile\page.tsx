'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Edit,
  Save,
  X,
  ArrowLeft,
  Shield,
  Package,
  CreditCard,
  ShoppingCart
} from 'lucide-react'
import { Navbar } from '@/components/layout/navbar'
import { Footer } from '@/components/layout/footer'
import { toast } from 'sonner'

export default function ProfilePage() {
  const { data: session, update } = useSession()
  const router = useRouter()
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
  })

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin?callbackUrl=/profile')
      return
    }

    // Initialize form data with session data
    setFormData({
      name: session.user?.name || '',
      email: session.user?.email || '',
      phone: session.user?.phone || '',
      address: session.user?.address || '',
    })
  }, [session, router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        // Update session
        await update({
          ...session,
          user: {
            ...session?.user,
            ...result.data
          }
        })

        setIsEditing(false)
        toast.success(result.message || 'Profil berhasil diperbarui')
      } else {
        throw new Error(result.error || 'Gagal memperbarui profil')
      }
    } catch (error) {
      console.error('Update profile error:', error)
      toast.error(error instanceof Error ? error.message : 'Terjadi kesalahan')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    // Reset form data
    setFormData({
      name: session?.user?.name || '',
      email: session?.user?.email || '',
      phone: session?.user?.phone || '',
      address: session?.user?.address || '',
    })
    setIsEditing(false)
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Beranda
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Profil Saya</h1>
            <p className="text-gray-600">Kelola informasi akun Anda</p>
          </div>
        </div>

        <div className="max-w-2xl mx-auto space-y-6">
          {/* Profile Info */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5 text-red-600" />
                  Informasi Profil
                </CardTitle>
                {!isEditing ? (
                  <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                ) : (
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={handleCancel}>
                      <X className="w-4 h-4 mr-2" />
                      Batal
                    </Button>
                    <Button size="sm" onClick={handleSave} disabled={isLoading}>
                      <Save className="w-4 h-4 mr-2" />
                      {isLoading ? 'Menyimpan...' : 'Simpan'}
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Role Badge */}
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-blue-600" />
                <Badge variant={session.user?.role === 'ADMIN' ? 'default' : 'secondary'}>
                  {session.user?.role === 'ADMIN' ? 'Administrator' : 'Customer'}
                </Badge>
              </div>

              {/* Name */}
              <div>
                <Label htmlFor="name">Nama Lengkap</Label>
                {isEditing ? (
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Masukkan nama lengkap"
                  />
                ) : (
                  <p className="mt-1 text-gray-900">{formData.name || 'Belum diisi'}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <Label htmlFor="email">Email</Label>
                {isEditing ? (
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Masukkan email"
                  />
                ) : (
                  <div className="flex items-center gap-2 mt-1">
                    <Mail className="w-4 h-4 text-gray-500" />
                    <p className="text-gray-900">{formData.email || 'Belum diisi'}</p>
                  </div>
                )}
              </div>

              {/* Phone */}
              <div>
                <Label htmlFor="phone">Nomor Telepon</Label>
                {isEditing ? (
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Masukkan nomor telepon"
                  />
                ) : (
                  <div className="flex items-center gap-2 mt-1">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <p className="text-gray-900">{formData.phone || 'Belum diisi'}</p>
                  </div>
                )}
              </div>

              {/* Address */}
              <div>
                <Label htmlFor="address">Alamat</Label>
                {isEditing ? (
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    placeholder="Masukkan alamat lengkap"
                    rows={3}
                  />
                ) : (
                  <div className="flex items-start gap-2 mt-1">
                    <MapPin className="w-4 h-4 text-gray-500 mt-0.5" />
                    <p className="text-gray-900">{formData.address || 'Belum diisi'}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Aksi Cepat</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Button variant="outline" asChild>
                  <Link href="/orders">
                    <Package className="w-4 h-4 mr-2" />
                    Pesanan Saya
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/customer/payments">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Riwayat Pembayaran
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/cart">
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    Keranjang Belanja
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/products">
                    <Package className="w-4 h-4 mr-2" />
                    Katalog Produk
                  </Link>
                </Button>
                {session.user?.role === 'ADMIN' && (
                  <Button variant="outline" asChild>
                    <Link href="/admin">
                      <Shield className="w-4 h-4 mr-2" />
                      Dashboard Admin
                    </Link>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Account Info */}
          <Card>
            <CardHeader>
              <CardTitle>Informasi Akun</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-gray-600">
                <p>ID Akun: {session.user?.id}</p>
                <p>Bergabung: {new Date(session.user?.createdAt || '').toLocaleDateString('id-ID')}</p>
                <p>Terakhir Login: {new Date().toLocaleDateString('id-ID')}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  )
}
