import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/customers - Get all customers for admin
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      role: 'CUSTOMER'
    }

    // Search functionality
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { email: { contains: search } },
        { phone: { contains: search } }
      ]
    }

    // Status filter - simplified approach
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

    // We'll filter by status after getting the data to avoid complex nested queries
    // This is more reliable for MySQL

    // Build orderBy - simplified
    const orderBy: any = { createdAt: 'desc' }

    // Get customers with order aggregations - simplified query
    const [allCustomers, total] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          _count: {
            select: {
              orders: true
            }
          },
          orders: {
            select: {
              total: true,
              createdAt: true,
              status: true
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        },
        orderBy
      }),
      prisma.user.count({ where })
    ])

    // Calculate aggregated data for each customer
    let customersWithStats = allCustomers.map(customer => {
      const paidOrders = customer.orders.filter(order =>
        order.status === 'PAYMENT_VERIFIED' ||
        order.status === 'SHIPPED' ||
        order.status === 'DELIVERED'
      )
      const totalSpent = paidOrders.reduce((sum, order) => sum + Number(order.total), 0)
      const lastOrderDate = customer.orders.length > 0 ? customer.orders[0].createdAt : null

      // Determine status
      let customerStatus = 'INACTIVE'
      if (customer.orders.length === 0) {
        customerStatus = 'NEW'
      } else if (customer.orders.some(o => new Date(o.createdAt) >= thirtyDaysAgo)) {
        customerStatus = 'ACTIVE'
      }

      return {
        id: customer.id,
        name: customer.name || customer.email,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        latitude: customer.latitude,
        longitude: customer.longitude,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
        _count: customer._count,
        totalSpent,
        lastOrderDate,
        status: customerStatus
      }
    })

    // Filter by status if specified
    if (status && status !== 'ALL') {
      if (status === 'active') {
        customersWithStats = customersWithStats.filter(c => c.status === 'ACTIVE')
      } else if (status === 'inactive') {
        customersWithStats = customersWithStats.filter(c => c.status === 'INACTIVE')
      } else if (status === 'new') {
        customersWithStats = customersWithStats.filter(c => c.status === 'NEW')
      }
    }

    // Sort by aggregated fields if needed
    if (sortBy === 'totalSpent') {
      customersWithStats.sort((a, b) => {
        return sortOrder === 'desc' ? b.totalSpent - a.totalSpent : a.totalSpent - b.totalSpent
      })
    } else if (sortBy === 'orderCount') {
      customersWithStats.sort((a, b) => {
        return sortOrder === 'desc' ? b._count.orders - a._count.orders : a._count.orders - b._count.orders
      })
    } else if (sortBy === 'lastOrderDate') {
      customersWithStats.sort((a, b) => {
        if (!a.lastOrderDate && !b.lastOrderDate) return 0
        if (!a.lastOrderDate) return sortOrder === 'desc' ? 1 : -1
        if (!b.lastOrderDate) return sortOrder === 'desc' ? -1 : 1

        const dateA = new Date(a.lastOrderDate).getTime()
        const dateB = new Date(b.lastOrderDate).getTime()
        return sortOrder === 'desc' ? dateB - dateA : dateA - dateB
      })
    }

    // Apply pagination after filtering and sorting
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedCustomers = customersWithStats.slice(startIndex, endIndex)
    const filteredTotal = customersWithStats.length

    // Calculate stats using the same data
    const stats = {
      totalCustomers: allCustomers.length,
      newCustomers: allCustomers.filter(c =>
        new Date(c.createdAt) >= thirtyDaysAgo
      ).length,
      activeCustomers: allCustomers.filter(c =>
        c.orders.some(o => new Date(o.createdAt) >= thirtyDaysAgo)
      ).length,
      topSpenders: allCustomers.filter(c => {
        const totalSpent = c.orders
          .filter(o =>
            o.status === 'PAYMENT_VERIFIED' ||
            o.status === 'SHIPPED' ||
            o.status === 'DELIVERED'
          )
          .reduce((sum, o) => sum + Number(o.total), 0)
        return totalSpent >= 500000
      }).length
    }

    return NextResponse.json({
      success: true,
      data: {
        customers: paginatedCustomers,
        stats,
        pagination: {
          page,
          limit,
          total: filteredTotal,
          totalPages: Math.ceil(filteredTotal / limit),
        }
      }
    })

  } catch (error) {
    console.error('Get admin customers error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
