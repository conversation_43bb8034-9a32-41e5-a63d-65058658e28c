'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'

import {
  MapPin,
  TrendingUp,
  Package,
  Clock,
  BarChart3,
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface DeliveryZoneData {
  zone: string
  address: string
  orderCount: number
  totalRevenue: number
  avgDeliveryFee: number
  lastOrderDate: string
}

interface DeliveryStats {
  totalDeliveries: number
  totalRevenue: number
  avgDeliveryFee: number
  topZone: {
    name: string
    orderCount: number
  }
}

export default function DeliveryAnalyticsPage() {
  const [stats, setStats] = useState<DeliveryStats | null>(null)
  const [zoneData, setZoneData] = useState<DeliveryZoneData[]>([])
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    fetchDeliveryAnalytics()
  }, [timeRange])

  const fetchDeliveryAnalytics = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/delivery/analytics?timeRange=${timeRange}`)
      const result = await response.json()

      if (result.success) {
        setStats(result.data.stats)
        setZoneData(result.data.zones)
      }
    } catch (error) {
      console.error('Error fetching delivery analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analisis Pengiriman</h1>
          <p className="text-gray-600">Insight lokasi dan frekuensi pengiriman pesanan.</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 Hari</SelectItem>
              <SelectItem value="30d">30 Hari</SelectItem>
              <SelectItem value="90d">90 Hari</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Pengiriman */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Pengiriman</p>
                <p className="text-2xl font-bold">
                  {loading ? '...' : stats?.totalDeliveries || 0}
                </p>
                <p className="text-sm text-gray-500 mt-1">Pesanan terkirim</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Package className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Revenue Pengiriman */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Revenue Pengiriman</p>
                <p className="text-2xl font-bold">
                  {loading ? '...' : formatCurrency(stats?.totalRevenue || 0)}
                </p>
                <p className="text-sm text-gray-500 mt-1">Total biaya kirim</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Rata-rata Biaya Kirim */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rata-rata Biaya Kirim</p>
                <p className="text-2xl font-bold">
                  {loading ? '...' : formatCurrency(stats?.avgDeliveryFee || 0)}
                </p>
                <p className="text-sm text-gray-500 mt-1">Per pengiriman</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Zona Terpopuler */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Zona Terpopuler</p>
                <p className="text-2xl font-bold truncate">
                  {loading ? '...' : stats?.topZone?.name || 'Belum ada data'}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  {loading ? '...' : stats?.topZone?.orderCount || 0} pesanan
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <MapPin className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delivery Zones Table */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center">
            <MapPin className="w-5 h-5 text-blue-600 mr-2" />
            Frekuensi Lokasi Pengiriman
          </CardTitle>
          <div className="text-sm text-gray-500">
            Filter: {timeRange === '7d' ? '7 Hari' : timeRange === '30d' ? '30 Hari' : '90 Hari'}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="h-[400px] flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-500">Memuat data pengiriman...</p>
              </div>
            </div>
          ) : zoneData.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Zona/Area</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Alamat Terakhir</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Jumlah Pesanan</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Total Revenue</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Rata-rata Biaya</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Terakhir Dikirim</th>
                  </tr>
                </thead>
                <tbody>
                  {zoneData.map((zone, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium">{zone.zone}</td>
                      <td className="py-3 px-4 text-gray-600 max-w-xs truncate">{zone.address}</td>
                      <td className="py-3 px-4">
                        <Badge variant="secondary">{zone.orderCount} pesanan</Badge>
                      </td>
                      <td className="py-3 px-4 font-medium">{formatCurrency(zone.totalRevenue)}</td>
                      <td className="py-3 px-4">{formatCurrency(zone.avgDeliveryFee)}</td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(zone.lastOrderDate).toLocaleDateString('id-ID')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="h-[400px] flex items-center justify-center text-gray-500">
              <div className="text-center">
                <MapPin className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Belum ada data pengiriman</p>
                <p className="text-sm">Data akan muncul setelah ada pesanan yang dikirim</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
