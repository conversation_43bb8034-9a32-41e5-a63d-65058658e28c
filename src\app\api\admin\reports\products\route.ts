import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { startOfDay, endOfDay, subDays } from 'date-fns'

// GET /api/admin/reports/products - Get product analytics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'
    const sortBy = searchParams.get('sortBy') || 'revenue'
    const category = searchParams.get('category')

    // Calculate date range
    let fromDate: Date
    let toDate = new Date()
    let previousFromDate: Date
    let previousToDate: Date

    switch (timeRange) {
      case '7d':
        fromDate = subDays(toDate, 7)
        previousFromDate = subDays(fromDate, 7)
        previousToDate = fromDate
        break
      case '30d':
        fromDate = subDays(toDate, 30)
        previousFromDate = subDays(fromDate, 30)
        previousToDate = fromDate
        break
      case '90d':
        fromDate = subDays(toDate, 90)
        previousFromDate = subDays(fromDate, 90)
        previousToDate = fromDate
        break
      case '1y':
        fromDate = subDays(toDate, 365)
        previousFromDate = subDays(fromDate, 365)
        previousToDate = fromDate
        break
      default:
        fromDate = subDays(toDate, 30)
        previousFromDate = subDays(fromDate, 30)
        previousToDate = fromDate
    }

    // Get all products with their performance data
    const products = await prisma.product.findMany({
      where: category ? { category: { name: category } } : {},
      include: {
        category: true,
        orderItems: {
          where: {
            order: {
              createdAt: {
                gte: startOfDay(fromDate),
                lte: endOfDay(toDate)
              },
              paymentStatus: 'PAID'
            }
          },
          include: {
            order: true
          }
        }
      }
    })

    // Get previous period data for comparison
    const previousPeriodItems = await prisma.orderItem.findMany({
      where: {
        order: {
          createdAt: {
            gte: startOfDay(previousFromDate),
            lte: endOfDay(previousToDate)
          },
          paymentStatus: 'PAID'
        }
      },
      include: {
        product: true,
        order: true
      }
    })

    // Calculate product performance
    const productPerformance = products.map(product => {
      const currentItems = product.orderItems
      const previousItems = previousPeriodItems.filter(item => item.productId === product.id)

      // Current period metrics
      const totalRevenue = currentItems.reduce((sum, item) => sum + Number(item.subtotal), 0)
      const totalQuantitySold = currentItems.reduce((sum, item) => sum + item.quantity, 0)
      const totalOrders = new Set(currentItems.map(item => item.orderId)).size
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

      // Previous period metrics
      const previousRevenue = previousItems.reduce((sum, item) => sum + Number(item.subtotal), 0)
      const previousQuantity = previousItems.reduce((sum, item) => sum + item.quantity, 0)

      // Growth calculations
      const revenueGrowth = previousRevenue > 0 
        ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 
        : totalRevenue > 0 ? 100 : 0

      const quantityGrowth = previousQuantity > 0 
        ? ((totalQuantitySold - previousQuantity) / previousQuantity) * 100 
        : totalQuantitySold > 0 ? 100 : 0

      return {
        id: product.id,
        name: product.name,
        images: product.images ? JSON.parse(product.images) : [],
        category: product.category.name,
        totalRevenue,
        totalQuantitySold,
        totalOrders,
        averageOrderValue,
        stock: product.stock,
        isActive: product.isActive,
        conversionRate: 0, // Would need view data to calculate
        revenueGrowth,
        quantityGrowth
      }
    })

    // Sort products based on sortBy parameter
    productPerformance.sort((a, b) => {
      switch (sortBy) {
        case 'revenue':
          return b.totalRevenue - a.totalRevenue
        case 'quantity':
          return b.totalQuantitySold - a.totalQuantitySold
        case 'orders':
          return b.totalOrders - a.totalOrders
        case 'growth':
          return b.revenueGrowth - a.revenueGrowth
        default:
          return b.totalRevenue - a.totalRevenue
      }
    })

    // Get top and worst performers
    const topPerformers = productPerformance.slice(0, 5)
    const worstPerformers = productPerformance
      .filter(p => p.totalRevenue > 0) // Only products with sales
      .slice(-5)
      .reverse()

    // Category performance analysis
    const categoryStats: { [key: string]: { revenue: number, quantity: number, products: number } } = {}
    
    productPerformance.forEach(product => {
      const category = product.category
      if (!categoryStats[category]) {
        categoryStats[category] = { revenue: 0, quantity: 0, products: 0 }
      }
      categoryStats[category].revenue += product.totalRevenue
      categoryStats[category].quantity += product.totalQuantitySold
      categoryStats[category].products += 1
    })

    const categoryPerformance = Object.entries(categoryStats).map(([category, stats]) => ({
      category,
      ...stats
    }))

    // Overall stats
    const totalProducts = products.length
    const activeProducts = products.filter(p => p.isActive).length
    const outOfStockProducts = products.filter(p => p.stock === 0).length
    const lowStockProducts = products.filter(p => p.stock > 0 && p.stock <= 10).length

    const stats = {
      totalProducts,
      activeProducts,
      outOfStockProducts,
      lowStockProducts,
      topPerformers,
      worstPerformers,
      categoryPerformance
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Get product report error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
