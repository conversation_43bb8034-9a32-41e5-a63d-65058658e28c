import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/notifications - Get admin notifications
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')

    // Get recent notifications from various sources
    const [
      pendingOrders,
      unreadMessages,
      lowStockProducts,
      recentOrders
    ] = await Promise.all([
      // Pending orders
      prisma.order.findMany({
        where: { status: 'PENDING_PAYMENT' },
        include: {
          user: {
            select: { name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      }),

      // Unread messages from customers
      prisma.message.findMany({
        where: {
          isRead: false,
          sender: { role: 'CUSTOMER' }
        },
        include: {
          sender: {
            select: { name: true, email: true }
          },
          conversation: true
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      }),

      // Low stock products
      prisma.product.findMany({
        where: {
          stock: { lte: 10 },
          isActive: true
        },
        orderBy: { stock: 'asc' },
        take: 5
      }),

      // Recent orders for activity feed
      prisma.order.findMany({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        include: {
          user: {
            select: { name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      })
    ])

    // Format notifications
    const notifications = []

    // Add pending order notifications
    pendingOrders.forEach(order => {
      notifications.push({
        id: `order-${order.id}`,
        type: 'order',
        title: 'Pesanan Baru',
        message: `Pesanan ${order.orderNumber} dari ${order.user.name || order.user.email}`,
        time: formatTimeAgo(order.createdAt),
        unread: true,
        createdAt: order.createdAt.toISOString(),
        link: `/admin/orders/${order.id}`
      })
    })

    // Add unread message notifications
    unreadMessages.forEach(message => {
      notifications.push({
        id: `message-${message.id}`,
        type: 'message',
        title: 'Pesan Baru',
        message: `Pesan dari ${message.sender.name || message.sender.email}: ${message.content.substring(0, 50)}...`,
        time: formatTimeAgo(message.createdAt),
        unread: true,
        createdAt: message.createdAt.toISOString(),
        link: `/admin/messages?conversation=${message.conversationId}`
      })
    })

    // Add low stock notifications
    lowStockProducts.forEach(product => {
      notifications.push({
        id: `stock-${product.id}`,
        type: 'stock',
        title: 'Stok Menipis',
        message: `${product.name} tersisa ${product.stock} unit`,
        time: 'Sekarang',
        unread: true,
        createdAt: new Date().toISOString(),
        link: `/admin/products/${product.id}`
      })
    })

    // Add recent order activity
    recentOrders.forEach(order => {
      notifications.push({
        id: `activity-${order.id}`,
        type: 'delivery',
        title: 'Aktivitas Pesanan',
        message: `Pesanan ${order.orderNumber} - Status: ${order.status}`,
        time: formatTimeAgo(order.updatedAt),
        unread: false,
        createdAt: order.updatedAt.toISOString(),
        link: `/admin/orders/${order.id}`
      })
    })

    // Sort by creation date and limit
    const sortedNotifications = notifications
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit)

    return NextResponse.json({
      success: true,
      data: sortedNotifications
    })

  } catch (error) {
    console.error('Get notifications error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to format time ago
function formatTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'Baru saja'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} menit lalu`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} jam lalu`
  } else {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} hari lalu`
  }
}
