import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { startOfDay, endOfDay, subDays, format } from 'date-fns'

// GET /api/admin/reports/sales - Get sales analytics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '7d'
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Calculate date range
    let fromDate: Date
    let toDate = new Date()
    let previousFromDate: Date
    let previousToDate: Date

    if (startDate && endDate) {
      fromDate = new Date(startDate)
      toDate = new Date(endDate)
      const daysDiff = Math.ceil((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24))
      previousFromDate = subDays(fromDate, daysDiff)
      previousToDate = subDays(toDate, daysDiff)
    } else {
      switch (timeRange) {
        case '7d':
          fromDate = subDays(toDate, 7)
          previousFromDate = subDays(fromDate, 7)
          previousToDate = fromDate
          break
        case '30d':
          fromDate = subDays(toDate, 30)
          previousFromDate = subDays(fromDate, 30)
          previousToDate = fromDate
          break
        case '90d':
          fromDate = subDays(toDate, 90)
          previousFromDate = subDays(fromDate, 90)
          previousToDate = fromDate
          break
        case '1y':
          fromDate = subDays(toDate, 365)
          previousFromDate = subDays(fromDate, 365)
          previousToDate = fromDate
          break
        default:
          fromDate = subDays(toDate, 7)
          previousFromDate = subDays(fromDate, 7)
          previousToDate = fromDate
      }
    }

    // Get current period data
    const [
      currentOrders,
      previousOrders,
      currentCustomers,
      previousCustomers
    ] = await Promise.all([
      // Current period orders
      prisma.order.findMany({
        where: {
          createdAt: {
            gte: startOfDay(fromDate),
            lte: endOfDay(toDate)
          },
          paymentStatus: 'PAID'
        },
        include: {
          user: true,
          orderItems: {
            include: {
              product: true
            }
          }
        }
      }),

      // Previous period orders for comparison
      prisma.order.findMany({
        where: {
          createdAt: {
            gte: startOfDay(previousFromDate),
            lte: endOfDay(previousToDate)
          },
          paymentStatus: 'PAID'
        }
      }),

      // Current period new customers
      prisma.user.count({
        where: {
          role: 'CUSTOMER',
          createdAt: {
            gte: startOfDay(fromDate),
            lte: endOfDay(toDate)
          }
        }
      }),

      // Previous period new customers
      prisma.user.count({
        where: {
          role: 'CUSTOMER',
          createdAt: {
            gte: startOfDay(previousFromDate),
            lte: endOfDay(previousToDate)
          }
        }
      })
    ])

    // Calculate stats
    const currentRevenue = currentOrders.reduce((sum, order) => sum + Number(order.totalAmount), 0)
    const previousRevenue = previousOrders.reduce((sum, order) => sum + Number(order.totalAmount), 0)
    
    const revenueGrowth = previousRevenue > 0 
      ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 
      : currentRevenue > 0 ? 100 : 0

    const ordersGrowth = previousOrders.length > 0 
      ? ((currentOrders.length - previousOrders.length) / previousOrders.length) * 100 
      : currentOrders.length > 0 ? 100 : 0

    const customersGrowth = previousCustomers > 0 
      ? ((currentCustomers - previousCustomers) / previousCustomers) * 100 
      : currentCustomers > 0 ? 100 : 0

    const averageOrderValue = currentOrders.length > 0 ? currentRevenue / currentOrders.length : 0

    // Generate chart data (daily aggregation)
    const chartData = []
    const currentDate = new Date(fromDate)
    
    while (currentDate <= toDate) {
      const dayStart = startOfDay(currentDate)
      const dayEnd = endOfDay(currentDate)
      
      const dayOrders = currentOrders.filter(order => {
        const orderDate = new Date(order.createdAt)
        return orderDate >= dayStart && orderDate <= dayEnd
      })

      const dayRevenue = dayOrders.reduce((sum, order) => sum + Number(order.totalAmount), 0)
      const dayCustomers = new Set(dayOrders.map(order => order.userId)).size
      const dayAOV = dayOrders.length > 0 ? dayRevenue / dayOrders.length : 0

      chartData.push({
        date: format(currentDate, 'MMM dd'),
        revenue: dayRevenue,
        orders: dayOrders.length,
        customers: dayCustomers,
        averageOrderValue: dayAOV
      })

      currentDate.setDate(currentDate.getDate() + 1)
    }

    // Top products analysis
    const productSales: { [key: string]: { name: string, revenue: number, quantity: number, orders: number } } = {}
    
    currentOrders.forEach(order => {
      order.orderItems.forEach(item => {
        const productId = item.productId
        const productName = item.product.name
        
        if (!productSales[productId]) {
          productSales[productId] = {
            name: productName,
            revenue: 0,
            quantity: 0,
            orders: 0
          }
        }
        
        productSales[productId].revenue += Number(item.subtotal)
        productSales[productId].quantity += item.quantity
        productSales[productId].orders += 1
      })
    })

    const topProducts = Object.values(productSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5)

    // Payment methods analysis
    const paymentMethods: { [key: string]: number } = {}
    currentOrders.forEach(order => {
      const method = order.paymentMethod || 'Unknown'
      paymentMethods[method] = (paymentMethods[method] || 0) + Number(order.totalAmount)
    })

    const totalPaymentRevenue = Object.values(paymentMethods).reduce((sum, amount) => sum + amount, 0)
    const paymentMethodsData = Object.entries(paymentMethods).map(([method, revenue]) => ({
      method,
      revenue,
      percentage: totalPaymentRevenue > 0 ? (revenue / totalPaymentRevenue) * 100 : 0
    }))

    const stats = {
      totalRevenue: currentRevenue,
      totalOrders: currentOrders.length,
      totalCustomers: currentCustomers,
      averageOrderValue,
      revenueGrowth,
      ordersGrowth,
      customersGrowth,
      topProducts,
      paymentMethods: paymentMethodsData
    }

    return NextResponse.json({
      success: true,
      data: {
        stats,
        chartData,
        period: {
          from: fromDate.toISOString(),
          to: toDate.toISOString(),
          timeRange
        }
      }
    })

  } catch (error) {
    console.error('Get sales report error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
