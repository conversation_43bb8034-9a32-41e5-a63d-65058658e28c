'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  MessageCircle,
  Send,
  Search,
  Filter,
  Clock,
  CheckCircle2,
  User,
  RefreshCw,
  Archive,
  Star,
  MoreVertical
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { toast } from 'sonner'

interface Message {
  id: string
  content: string
  senderId: string
  senderName: string
  senderRole: 'CUSTOMER' | 'ADMIN'
  createdAt: string
  isRead: boolean
}

interface Conversation {
  id: string
  customerId: string
  customerName: string
  customerEmail: string
  lastMessage?: Message
  unreadCount: number
  status: 'ACTIVE' | 'CLOSED'
  priority: 'LOW' | 'NORMAL' | 'HIGH'
  createdAt: string
  updatedAt: string
  messages: Message[]
}

export default function AdminMessagesPage() {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null)
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [priorityFilter, setPriorityFilter] = useState('ALL')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    fetchConversations()

    // Setup real-time polling
    const interval = setInterval(fetchConversations, 5000)
    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [activeConversation?.messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const fetchConversations = async () => {
    try {
      const response = await fetch('/api/messages/conversations')
      const result = await response.json()

      if (response.ok && result.success) {
        setConversations(result.data)
      }
    } catch (error) {
      console.error('Error fetching conversations:', error)
    } finally {
      setLoading(false)
    }
  }

  const selectConversation = async (conversation: Conversation) => {
    setActiveConversation(conversation)

    // Mark messages as read
    if (conversation.unreadCount > 0) {
      try {
        await fetch(`/api/messages/conversations/${conversation.id}/read`, {
          method: 'PUT'
        })

        // Update local state
        setConversations(prev => prev.map(conv =>
          conv.id === conversation.id
            ? { ...conv, unreadCount: 0 }
            : conv
        ))
      } catch (error) {
        console.error('Error marking as read:', error)
      }
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || !activeConversation || sending) return

    setSending(true)
    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversationId: activeConversation.id,
          content: newMessage.trim()
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setNewMessage('')

        // Update active conversation with new message
        setActiveConversation(prev => prev ? {
          ...prev,
          messages: [...prev.messages, result.data]
        } : null)

        // Update conversations list
        setConversations(prev => prev.map(conv =>
          conv.id === activeConversation.id
            ? { ...conv, lastMessage: result.data, updatedAt: result.data.createdAt }
            : conv
        ))

        toast.success('Pesan berhasil dikirim')
      } else {
        toast.error(result.error || 'Gagal mengirim pesan')
      }
    } catch (error) {
      console.error('Error sending message:', error)
      toast.error('Gagal mengirim pesan')
    } finally {
      setSending(false)
    }
  }

  const updateConversationStatus = async (conversationId: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/conversations/${conversationId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      })

      if (response.ok) {
        setConversations(prev => prev.map(conv =>
          conv.id === conversationId ? { ...conv, status: status as 'ACTIVE' | 'CLOSED' } : conv
        ))
        toast.success('Status percakapan berhasil diperbarui')
      }
    } catch (error) {
      console.error('Error updating conversation:', error)
      toast.error('Gagal memperbarui status')
    }
  }

  const getPriorityBadge = (priority: string) => {
    const config = {
      LOW: { label: 'Rendah', variant: 'secondary' as const },
      NORMAL: { label: 'Normal', variant: 'default' as const },
      HIGH: { label: 'Tinggi', variant: 'destructive' as const }
    }

    const { label, variant } = config[priority as keyof typeof config] || config.NORMAL
    return <Badge variant={variant} className="text-xs">{label}</Badge>
  }

  const filteredConversations = conversations.filter(conv => {
    const matchesSearch = !searchTerm ||
      conv.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      conv.customerEmail.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'ALL' || conv.status === statusFilter
    const matchesPriority = priorityFilter === 'ALL' || conv.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesPriority
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Pesan Customer</h1>
          <p className="text-gray-600">Kelola komunikasi dengan customer</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={fetchConversations}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid lg:grid-cols-4 gap-6 h-[700px]">
        {/* Conversations List */}
        <div className="lg:col-span-1">
          <Card className="h-full flex flex-col">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <MessageCircle className="w-5 h-5 text-blue-600" />
                  Percakapan ({filteredConversations.length})
                </span>
              </CardTitle>

              {/* Filters */}
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Cari customer..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 text-sm"
                  />
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="text-xs">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">Semua</SelectItem>
                      <SelectItem value="ACTIVE">Aktif</SelectItem>
                      <SelectItem value="CLOSED">Ditutup</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger className="text-xs">
                      <SelectValue placeholder="Prioritas" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">Semua</SelectItem>
                      <SelectItem value="HIGH">Tinggi</SelectItem>
                      <SelectItem value="NORMAL">Normal</SelectItem>
                      <SelectItem value="LOW">Rendah</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>

            <CardContent className="flex-1 p-0 overflow-hidden">
              <div className="h-full overflow-y-auto">
                {loading ? (
                  <div className="p-4 space-y-3">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="flex items-center gap-3 p-3">
                          <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                          <div className="flex-1 space-y-2">
                            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                            <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : filteredConversations.length > 0 ? (
                  <div className="space-y-1">
                    {filteredConversations.map((conversation) => (
                      <div
                        key={conversation.id}
                        className={`p-4 cursor-pointer hover:bg-gray-50 border-b transition-colors ${
                          activeConversation?.id === conversation.id ? 'bg-blue-50 border-blue-200' : ''
                        }`}
                        onClick={() => selectConversation(conversation)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <Avatar className="w-8 h-8 flex-shrink-0">
                              <AvatarFallback className="text-xs">
                                {conversation.customerName.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className="min-w-0 flex-1">
                              <p className="font-medium text-sm truncate">{conversation.customerName}</p>
                              <p className="text-xs text-gray-500 truncate">{conversation.customerEmail}</p>
                            </div>
                          </div>

                          <div className="flex flex-col items-end gap-1 flex-shrink-0">
                            {conversation.unreadCount > 0 && (
                              <Badge variant="destructive" className="text-xs px-1.5 py-0.5">
                                {conversation.unreadCount}
                              </Badge>
                            )}
                            {getPriorityBadge(conversation.priority)}
                          </div>
                        </div>

                        {conversation.lastMessage && (
                          <div className="space-y-1">
                            <p className="text-xs text-gray-600 line-clamp-2">
                              {conversation.lastMessage.content}
                            </p>
                            <div className="flex items-center justify-between">
                              <p className="text-xs text-gray-400">
                                {formatDistanceToNow(new Date(conversation.updatedAt), {
                                  addSuffix: true,
                                  locale: id
                                })}
                              </p>
                              <Badge variant={conversation.status === 'ACTIVE' ? 'default' : 'secondary'} className="text-xs">
                                {conversation.status === 'ACTIVE' ? 'Aktif' : 'Ditutup'}
                              </Badge>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-8 text-center">
                    <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-600">Tidak ada percakapan</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chat Area */}
        <div className="lg:col-span-3">
          <Card className="h-full flex flex-col">
            {activeConversation ? (
              <>
                {/* Chat Header */}
                <CardHeader className="border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarFallback>
                          {activeConversation.customerName.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{activeConversation.customerName}</h3>
                        <p className="text-sm text-gray-600">{activeConversation.customerEmail}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {getPriorityBadge(activeConversation.priority)}
                      <Select
                        value={activeConversation.status}
                        onValueChange={(value) => updateConversationStatus(activeConversation.id, value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ACTIVE">Aktif</SelectItem>
                          <SelectItem value="CLOSED">Ditutup</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardHeader>

                {/* Messages */}
                <CardContent className="flex-1 p-4 overflow-y-auto">
                  <div className="space-y-4">
                    {activeConversation.messages.map((message) => {
                      const isAdmin = message.senderRole === 'ADMIN'
                      return (
                        <div
                          key={message.id}
                          className={`flex ${isAdmin ? 'justify-end' : 'justify-start'}`}
                        >
                          <div
                            className={`max-w-[70%] p-3 rounded-lg ${
                              isAdmin
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-100 text-gray-900'
                            }`}
                          >
                            <p className="text-sm">{message.content}</p>
                            <div className={`flex items-center gap-1 mt-1 text-xs ${
                              isAdmin ? 'text-blue-100' : 'text-gray-500'
                            }`}>
                              <Clock className="w-3 h-3" />
                              {new Date(message.createdAt).toLocaleTimeString('id-ID', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                              {isAdmin && message.isRead && (
                                <CheckCircle2 className="w-3 h-3 ml-1" />
                              )}
                            </div>
                          </div>
                        </div>
                      )
                    })}
                    <div ref={messagesEndRef} />
                  </div>
                </CardContent>

                {/* Message Input */}
                <div className="border-t p-4">
                  <div className="flex gap-2">
                    <Textarea
                      placeholder="Ketik balasan Anda..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault()
                          sendMessage()
                        }
                      }}
                      rows={2}
                      className="flex-1 resize-none"
                      disabled={activeConversation.status === 'CLOSED'}
                    />
                    <Button
                      onClick={sendMessage}
                      disabled={!newMessage.trim() || sending || activeConversation.status === 'CLOSED'}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>

                  {activeConversation.status === 'CLOSED' && (
                    <p className="text-sm text-gray-500 mt-2">
                      Percakapan ini telah ditutup. Ubah status ke "Aktif" untuk melanjutkan.
                    </p>
                  )}
                </div>
              </>
            ) : (
              <CardContent className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Pilih Percakapan
                  </h3>
                  <p className="text-gray-600">
                    Pilih percakapan dari daftar untuk mulai membalas pesan customer
                  </p>
                </div>
              </CardContent>
            )}
          </Card>
        </div>
      </div>
    </div>
  )
}
