import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import { Toaster } from "@/components/ui/sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Acikoo Store - E-commerce Platform",
  description: "Comprehensive e-commerce platform with integrated admin dashboard",
  keywords: ["e-commerce", "online store", "delivery", "QRIS payment"],
  authors: [{ name: "Acikoo Team" }],
  creator: "<PERSON><PERSON>ko<PERSON>",
  publisher: "Acikoo",
  icons: {
    icon: "/img/logo.jpeg",
    shortcut: "/img/logo.jpeg",
    apple: "/img/logo.jpeg",
  },
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="id" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
