'use client'

import { useState, useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { 
  ArrowLeft,
  Send,
  MessageCircle,
  Clock,
  CheckCircle2,
  User,
  Shield,
  Phone,
  Mail
} from 'lucide-react'
import { Navbar } from '@/components/layout/navbar'
import { Footer } from '@/components/layout/footer'
import { toast } from 'sonner'

interface Message {
  id: string
  content: string
  senderId: string
  senderName: string
  senderRole: 'CUSTOMER' | 'ADMIN'
  createdAt: string
  isRead: boolean
}

interface Conversation {
  id: string
  customerId: string
  customerName: string
  lastMessage?: Message
  unreadCount: number
  status: 'ACTIVE' | 'CLOSED'
  createdAt: string
  updatedAt: string
  messages: Message[]
}

export default function MessagesPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null)
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin?callbackUrl=/messages')
      return
    }

    fetchConversations()
    
    // Setup real-time polling (in production, use WebSocket)
    const interval = setInterval(fetchConversations, 5000)
    return () => clearInterval(interval)
  }, [session, router])

  useEffect(() => {
    scrollToBottom()
  }, [activeConversation?.messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const fetchConversations = async () => {
    try {
      const response = await fetch('/api/messages/conversations')
      const result = await response.json()

      if (response.ok && result.success) {
        setConversations(result.data)
        
        // If no active conversation and user is customer, auto-create/select conversation
        if (!activeConversation && session?.user?.role === 'CUSTOMER' && result.data.length > 0) {
          setActiveConversation(result.data[0])
        }
      }
    } catch (error) {
      console.error('Error fetching conversations:', error)
    } finally {
      setLoading(false)
    }
  }

  const createOrSelectConversation = async () => {
    if (session?.user?.role === 'ADMIN') return

    try {
      const response = await fetch('/api/messages/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setActiveConversation(result.data)
        setConversations(prev => {
          const exists = prev.find(c => c.id === result.data.id)
          return exists ? prev : [result.data, ...prev]
        })
      }
    } catch (error) {
      console.error('Error creating conversation:', error)
      toast.error('Gagal membuat percakapan')
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || !activeConversation || sending) return

    setSending(true)
    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversationId: activeConversation.id,
          content: newMessage.trim()
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setNewMessage('')
        
        // Update active conversation with new message
        setActiveConversation(prev => prev ? {
          ...prev,
          messages: [...prev.messages, result.data]
        } : null)

        // Update conversations list
        setConversations(prev => prev.map(conv => 
          conv.id === activeConversation.id 
            ? { ...conv, lastMessage: result.data, updatedAt: result.data.createdAt }
            : conv
        ))
      } else {
        toast.error(result.error || 'Gagal mengirim pesan')
      }
    } catch (error) {
      console.error('Error sending message:', error)
      toast.error('Gagal mengirim pesan')
    } finally {
      setSending(false)
    }
  }

  const markAsRead = async (conversationId: string) => {
    try {
      await fetch(`/api/messages/conversations/${conversationId}/read`, {
        method: 'PUT'
      })
    } catch (error) {
      console.error('Error marking as read:', error)
    }
  }

  if (!session) return null

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
        <Navbar />
        <main className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  const isAdmin = session.user?.role === 'ADMIN'

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Beranda
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isAdmin ? 'Pesan Customer' : 'Hubungi Admin'}
            </h1>
            <p className="text-gray-600">
              {isAdmin ? 'Kelola percakapan dengan customer' : 'Tanyakan apapun tentang produk atau pesanan Anda'}
            </p>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-6 h-[600px]">
          {/* Conversations List (Admin) or Contact Info (Customer) */}
          <div className="lg:col-span-1">
            {isAdmin ? (
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageCircle className="w-5 h-5 text-blue-600" />
                    Percakapan ({conversations.length})
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="space-y-1 max-h-[500px] overflow-y-auto">
                    {conversations.map((conversation) => (
                      <div
                        key={conversation.id}
                        className={`p-4 cursor-pointer hover:bg-gray-50 border-b ${
                          activeConversation?.id === conversation.id ? 'bg-blue-50 border-blue-200' : ''
                        }`}
                        onClick={() => {
                          setActiveConversation(conversation)
                          markAsRead(conversation.id)
                        }}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Avatar className="w-8 h-8">
                              <AvatarFallback>
                                {conversation.customerName.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span className="font-medium text-sm">{conversation.customerName}</span>
                          </div>
                          {conversation.unreadCount > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              {conversation.unreadCount}
                            </Badge>
                          )}
                        </div>
                        {conversation.lastMessage && (
                          <p className="text-xs text-gray-600 truncate">
                            {conversation.lastMessage.content}
                          </p>
                        )}
                        <p className="text-xs text-gray-400 mt-1">
                          {new Date(conversation.updatedAt).toLocaleString('id-ID')}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="w-5 h-5 text-red-600" />
                    Kontak Admin
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center p-6 bg-gradient-to-br from-red-50 to-orange-50 rounded-lg">
                    <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <User className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">Tim Customer Service</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Siap membantu Anda 24/7 untuk pertanyaan produk, pesanan, atau keluhan
                    </p>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                      <Phone className="w-5 h-5 text-green-600" />
                      <div>
                        <p className="font-medium text-sm">WhatsApp</p>
                        <p className="text-sm text-gray-600">+62 812-3456-7890</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                      <Mail className="w-5 h-5 text-blue-600" />
                      <div>
                        <p className="font-medium text-sm">Email</p>
                        <p className="text-sm text-gray-600"><EMAIL></p>
                      </div>
                    </div>
                  </div>

                  {!activeConversation && (
                    <Button 
                      onClick={createOrSelectConversation}
                      className="w-full bg-red-600 hover:bg-red-700"
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Mulai Chat dengan Admin
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Chat Area */}
          <div className="lg:col-span-2">
            <Card className="h-full flex flex-col">
              {activeConversation ? (
                <>
                  {/* Chat Header */}
                  <CardHeader className="border-b">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarFallback>
                            {isAdmin 
                              ? activeConversation.customerName.charAt(0).toUpperCase()
                              : 'CS'
                            }
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-semibold">
                            {isAdmin ? activeConversation.customerName : 'Customer Service'}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {isAdmin ? 'Customer' : 'Tim Support Acikoo'}
                          </p>
                        </div>
                      </div>
                      <Badge variant={activeConversation.status === 'ACTIVE' ? 'default' : 'secondary'}>
                        {activeConversation.status === 'ACTIVE' ? 'Aktif' : 'Ditutup'}
                      </Badge>
                    </div>
                  </CardHeader>

                  {/* Messages */}
                  <CardContent className="flex-1 p-4 overflow-y-auto">
                    <div className="space-y-4">
                      {activeConversation.messages.map((message) => {
                        const isOwnMessage = message.senderId === session.user?.id
                        return (
                          <div
                            key={message.id}
                            className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-[70%] p-3 rounded-lg ${
                                isOwnMessage
                                  ? 'bg-red-600 text-white'
                                  : 'bg-gray-100 text-gray-900'
                              }`}
                            >
                              <p className="text-sm">{message.content}</p>
                              <div className={`flex items-center gap-1 mt-1 text-xs ${
                                isOwnMessage ? 'text-red-100' : 'text-gray-500'
                              }`}>
                                <Clock className="w-3 h-3" />
                                {new Date(message.createdAt).toLocaleTimeString('id-ID', {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                                {isOwnMessage && message.isRead && (
                                  <CheckCircle2 className="w-3 h-3 ml-1" />
                                )}
                              </div>
                            </div>
                          </div>
                        )
                      })}
                      <div ref={messagesEndRef} />
                    </div>
                  </CardContent>

                  {/* Message Input */}
                  <div className="border-t p-4">
                    <div className="flex gap-2">
                      <Textarea
                        placeholder="Ketik pesan Anda..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault()
                            sendMessage()
                          }
                        }}
                        rows={2}
                        className="flex-1 resize-none"
                      />
                      <Button
                        onClick={sendMessage}
                        disabled={!newMessage.trim() || sending}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        <Send className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <CardContent className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {isAdmin ? 'Pilih Percakapan' : 'Mulai Percakapan'}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {isAdmin 
                        ? 'Pilih percakapan dari daftar untuk mulai membalas pesan customer'
                        : 'Klik tombol di samping untuk memulai chat dengan admin'
                      }
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
