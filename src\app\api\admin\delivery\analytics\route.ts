import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { subDays, startOfDay } from 'date-fns'

// GET /api/admin/delivery/analytics - Get delivery analytics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'

    // Calculate date range
    const now = new Date()
    let startDate: Date

    switch (timeRange) {
      case '7d':
        startDate = startOfDay(subDays(now, 7))
        break
      case '90d':
        startDate = startOfDay(subDays(now, 90))
        break
      case '30d':
      default:
        startDate = startOfDay(subDays(now, 30))
        break
    }

    // Get delivered orders with location data
    const deliveredOrders = await prisma.order.findMany({
      where: {
        createdAt: { gte: startDate },
        status: { in: ['DELIVERED'] },
        deliveryLat: { not: null },
        deliveryLng: { not: null }
      },
      select: {
        id: true,
        deliveryAddress: true,
        deliveryLat: true,
        deliveryLng: true,
        deliveryFee: true,
        total: true,
        createdAt: true
      }
    })

    // Calculate stats
    const totalDeliveries = deliveredOrders.length
    const totalRevenue = deliveredOrders.reduce((sum, order) => sum + Number(order.deliveryFee || 0), 0)
    const avgDeliveryFee = totalDeliveries > 0 ? totalRevenue / totalDeliveries : 0

    // Group by delivery zones (simplified by area/district)
    const zoneMap = new Map<string, {
      zone: string
      addresses: string[]
      orderCount: number
      totalRevenue: number
      lastOrderDate: Date
    }>()

    deliveredOrders.forEach(order => {
      // Extract zone from address (simplified - take first part before comma)
      const addressParts = order.deliveryAddress?.split(',') || ['Unknown Area']
      const zone = addressParts[0]?.trim() || 'Unknown Area'
      
      if (!zoneMap.has(zone)) {
        zoneMap.set(zone, {
          zone,
          addresses: [],
          orderCount: 0,
          totalRevenue: 0,
          lastOrderDate: order.createdAt
        })
      }

      const zoneData = zoneMap.get(zone)!
      zoneData.orderCount += 1
      zoneData.totalRevenue += Number(order.deliveryFee || 0)
      zoneData.addresses.push(order.deliveryAddress || '')
      
      if (order.createdAt > zoneData.lastOrderDate) {
        zoneData.lastOrderDate = order.createdAt
      }
    })

    // Convert to array and sort by order count
    const zones = Array.from(zoneMap.values())
      .map(zone => ({
        zone: zone.zone,
        address: zone.addresses[zone.addresses.length - 1], // Latest address
        orderCount: zone.orderCount,
        totalRevenue: zone.totalRevenue,
        avgDeliveryFee: zone.orderCount > 0 ? zone.totalRevenue / zone.orderCount : 0,
        lastOrderDate: zone.lastOrderDate.toISOString()
      }))
      .sort((a, b) => b.orderCount - a.orderCount)

    // Find top zone
    const topZone = zones.length > 0 ? {
      name: zones[0].zone,
      orderCount: zones[0].orderCount
    } : null

    const stats = {
      totalDeliveries,
      totalRevenue,
      avgDeliveryFee,
      topZone
    }

    return NextResponse.json({
      success: true,
      data: {
        stats,
        zones
      }
    })

  } catch (error) {
    console.error('Get delivery analytics error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
