// Test locations in Jakarta for OpenStreetMap + Leaflet implementation
import { geocodeAddress, getDeliveryInfo, calculateDistance, STORE_LOCATION } from './leaflet-maps'

export interface TestLocation {
  name: string
  address: string
  expectedCoordinates?: {
    lat: number
    lng: number
  }
  expectedDeliverable: boolean
  expectedZone?: number
  description: string
}

// Test locations around Jakarta
export const JAKARTA_TEST_LOCATIONS: TestLocation[] = [
  // Zone 1 (0-3km) - Should be deliverable with Rp 5,000 fee
  {
    name: 'Pluit Village',
    address: 'Pluit Village, Pluit, Jakarta Utara',
    expectedCoordinates: { lat: -6.1289, lng: 106.7889 },
    expectedDeliverable: true,
    expectedZone: 1,
    description: 'Very close to store, same area'
  },
  {
    name: 'Pantai Indah Kapuk',
    address: 'Pantai Indah Kapuk, Jakarta Utara',
    expectedCoordinates: { lat: -6.1167, lng: 106.7500 },
    expectedDeliverable: true,
    expectedZone: 1,
    description: 'Nearby upscale area'
  },

  // Zone 2 (3-6km) - Should be deliverable with Rp 8,000 fee
  {
    name: '<PERSON>lapa Gading',
    address: 'Kelapa Gading, Jakarta Utara',
    expectedCoordinates: { lat: -6.1478, lng: 106.8917 },
    expectedDeliverable: true,
    expectedZone: 2,
    description: 'Popular shopping area in North Jakarta'
  },
  {
    name: 'Sunter',
    address: 'Sunter, Jakarta Utara',
    expectedCoordinates: { lat: -6.1389, lng: 106.8639 },
    expectedDeliverable: true,
    expectedZone: 2,
    description: 'Business district in North Jakarta'
  },
  {
    name: 'Grogol',
    address: 'Grogol, Jakarta Barat',
    expectedCoordinates: { lat: -6.1867, lng: 106.7456 },
    expectedDeliverable: true,
    expectedZone: 2,
    description: 'West Jakarta area'
  },

  // Zone 3 (6-9km) - Should be deliverable with Rp 12,000 fee
  {
    name: 'Menteng',
    address: 'Menteng, Jakarta Pusat',
    expectedCoordinates: { lat: -6.1944, lng: 106.8229 },
    expectedDeliverable: true,
    expectedZone: 3,
    description: 'Central Jakarta upscale area'
  },
  {
    name: 'Kemang',
    address: 'Kemang, Jakarta Selatan',
    expectedCoordinates: { lat: -6.2615, lng: 106.8106 },
    expectedDeliverable: true,
    expectedZone: 3,
    description: 'South Jakarta trendy area'
  },
  {
    name: 'Cikini',
    address: 'Cikini, Jakarta Pusat',
    expectedCoordinates: { lat: -6.1944, lng: 106.8417 },
    expectedDeliverable: true,
    expectedZone: 3,
    description: 'Central Jakarta area'
  },

  // Outside delivery range (>9km) - Should NOT be deliverable
  {
    name: 'Bekasi',
    address: 'Bekasi, Jawa Barat',
    expectedCoordinates: { lat: -6.2349, lng: 106.9896 },
    expectedDeliverable: false,
    description: 'Outside Jakarta, too far from store'
  },
  {
    name: 'Tangerang',
    address: 'Tangerang, Banten',
    expectedCoordinates: { lat: -6.1783, lng: 106.6319 },
    expectedDeliverable: false,
    description: 'Outside Jakarta, too far from store'
  },
  {
    name: 'Depok',
    address: 'Depok, Jawa Barat',
    expectedCoordinates: { lat: -6.4025, lng: 106.7942 },
    expectedDeliverable: false,
    description: 'South of Jakarta, too far from store'
  },

  // Edge cases - Borderline locations
  {
    name: 'Ancol',
    address: 'Ancol, Jakarta Utara',
    expectedCoordinates: { lat: -6.1222, lng: 106.8417 },
    expectedDeliverable: true,
    expectedZone: 2,
    description: 'Tourist area, should be reachable'
  },
  {
    name: 'Taman Anggrek',
    address: 'Taman Anggrek, Jakarta Barat',
    expectedCoordinates: { lat: -6.1778, lng: 106.7917 },
    expectedDeliverable: true,
    expectedZone: 2,
    description: 'Shopping mall area'
  }
]

// Test function to validate geocoding and delivery calculation
export async function testJakartaLocations(): Promise<void> {
  console.log('🗺️ Testing Jakarta Locations with OpenStreetMap + Leaflet')
  console.log('=' * 60)
  console.log(`📍 Store Location: ${STORE_LOCATION.lat}, ${STORE_LOCATION.lng}`)
  console.log('=' * 60)

  for (const location of JAKARTA_TEST_LOCATIONS) {
    console.log(`\n🧪 Testing: ${location.name}`)
    console.log(`📍 Address: ${location.address}`)
    
    try {
      // Test geocoding
      const coordinates = await geocodeAddress(location.address)
      
      if (!coordinates) {
        console.log('❌ Geocoding failed - address not found')
        continue
      }

      console.log(`📍 Coordinates: ${coordinates.lat.toFixed(4)}, ${coordinates.lng.toFixed(4)}`)

      // Calculate distance from store
      const distance = calculateDistance(STORE_LOCATION, coordinates)
      console.log(`📏 Distance: ${distance.toFixed(2)} km`)

      // Test delivery info
      const deliveryInfo = getDeliveryInfo(coordinates)
      console.log(`🚚 Deliverable: ${deliveryInfo.isDeliverable ? '✅ Yes' : '❌ No'}`)
      
      if (deliveryInfo.isDeliverable) {
        console.log(`💰 Delivery Fee: Rp ${deliveryInfo.fee.toLocaleString()}`)
        console.log(`⏱️ Estimated Time: ${deliveryInfo.estimatedTime}`)
        
        // Determine zone
        let zone = 3
        if (distance <= 3) zone = 1
        else if (distance <= 6) zone = 2
        
        console.log(`🎯 Zone: ${zone}`)
      }

      // Validate against expected results
      if (location.expectedDeliverable !== deliveryInfo.isDeliverable) {
        console.log(`⚠️ WARNING: Expected deliverable=${location.expectedDeliverable}, got ${deliveryInfo.isDeliverable}`)
      }

      if (location.expectedCoordinates) {
        const latDiff = Math.abs(coordinates.lat - location.expectedCoordinates.lat)
        const lngDiff = Math.abs(coordinates.lng - location.expectedCoordinates.lng)
        
        if (latDiff > 0.01 || lngDiff > 0.01) {
          console.log(`⚠️ WARNING: Coordinates differ significantly from expected`)
          console.log(`   Expected: ${location.expectedCoordinates.lat}, ${location.expectedCoordinates.lng}`)
          console.log(`   Got: ${coordinates.lat}, ${coordinates.lng}`)
        }
      }

      console.log(`ℹ️ ${location.description}`)

    } catch (error) {
      console.log(`❌ Error testing ${location.name}:`, error)
    }
  }

  console.log('\n' + '=' * 60)
  console.log('✅ Jakarta Location Testing Complete')
  console.log('=' * 60)
}

// Quick test function for specific address
export async function testSingleAddress(address: string): Promise<void> {
  console.log(`🧪 Testing single address: ${address}`)
  
  try {
    const coordinates = await geocodeAddress(address)
    
    if (!coordinates) {
      console.log('❌ Address not found')
      return
    }

    console.log(`📍 Coordinates: ${coordinates.lat.toFixed(4)}, ${coordinates.lng.toFixed(4)}`)
    
    const distance = calculateDistance(STORE_LOCATION, coordinates)
    console.log(`📏 Distance from store: ${distance.toFixed(2)} km`)
    
    const deliveryInfo = getDeliveryInfo(coordinates)
    console.log(`🚚 Deliverable: ${deliveryInfo.isDeliverable ? '✅ Yes' : '❌ No'}`)
    
    if (deliveryInfo.isDeliverable) {
      console.log(`💰 Delivery Fee: Rp ${deliveryInfo.fee.toLocaleString()}`)
      console.log(`⏱️ Estimated Time: ${deliveryInfo.estimatedTime}`)
    }

  } catch (error) {
    console.log('❌ Error:', error)
  }
}

// Export for use in development/testing
export { testJakartaLocations as default }
