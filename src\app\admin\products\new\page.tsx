'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { 
  ArrowLeft,
  Upload,
  X,
  Package,
  Save,
  Eye
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { toast } from 'sonner'
import { getProductImage } from '@/lib/product-images'

interface Category {
  id: string
  name: string
}

interface ProductForm {
  name: string
  description: string
  price: number
  categoryId: string
  stock: number
  isActive: boolean
  images: string[]
}

export default function AddProductPage() {
  const router = useRouter()
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [form, setForm] = useState<ProductForm>({
    name: '',
    description: '',
    price: 0,
    categoryId: '',
    stock: 0,
    isActive: true,
    images: []
  })

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      const result = await response.json()

      if (response.ok && result.success) {
        setCategories(result.data)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const handleImageUpload = async (files: FileList) => {
    if (!files || files.length === 0) return

    try {
      setUploading(true)
      const formData = new FormData()
      
      Array.from(files).forEach((file) => {
        formData.append('files', file)
      })
      formData.append('folder', 'products')

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setForm(prev => ({
          ...prev,
          images: [...prev.images, ...result.data.urls]
        }))
        toast.success(`${result.data.count} gambar berhasil diupload`)
      } else {
        toast.error(result.error || 'Gagal upload gambar')
      }
    } catch (error) {
      console.error('Error uploading images:', error)
      toast.error('Terjadi kesalahan saat upload gambar')
    } finally {
      setUploading(false)
    }
  }

  const removeImage = (index: number) => {
    setForm(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!form.name || !form.categoryId || form.price <= 0) {
      toast.error('Mohon lengkapi semua field yang wajib diisi')
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(form)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast.success('Produk berhasil ditambahkan')
        router.push('/admin/products')
      } else {
        toast.error(result.error || 'Gagal menambahkan produk')
      }
    } catch (error) {
      console.error('Error creating product:', error)
      toast.error('Terjadi kesalahan saat menambahkan produk')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/products">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Tambah Produk Baru</h1>
          <p className="text-gray-600">Lengkapi informasi produk yang akan ditambahkan</p>
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Informasi Dasar</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="name">Nama Produk *</Label>
                  <Input
                    id="name"
                    value={form.name}
                    onChange={(e) => setForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Masukkan nama produk"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea
                    id="description"
                    value={form.description}
                    onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Masukkan deskripsi produk"
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="category">Kategori *</Label>
                  <Select
                    value={form.categoryId}
                    onValueChange={(value) => setForm(prev => ({ ...prev, categoryId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih kategori" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Pricing & Inventory */}
            <Card>
              <CardHeader>
                <CardTitle>Harga & Stok</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="price">Harga *</Label>
                    <Input
                      id="price"
                      type="number"
                      value={form.price}
                      onChange={(e) => setForm(prev => ({ ...prev, price: Number(e.target.value) }))}
                      placeholder="0"
                      min="0"
                      required
                    />
                    {form.price > 0 && (
                      <p className="text-sm text-gray-600 mt-1">
                        {formatCurrency(form.price)}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="stock">Stok Awal *</Label>
                    <Input
                      id="stock"
                      type="number"
                      value={form.stock}
                      onChange={(e) => setForm(prev => ({ ...prev, stock: Number(e.target.value) }))}
                      placeholder="0"
                      min="0"
                      required
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Images */}
            <Card>
              <CardHeader>
                <CardTitle>Gambar Produk</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Upload Area */}
                <div className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors rounded-lg p-8 text-center">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
                    className="hidden"
                    id="image-upload"
                    disabled={uploading}
                  />
                  <label htmlFor="image-upload" className="cursor-pointer block">
                    <Upload className={`w-12 h-12 mx-auto mb-4 ${uploading ? 'text-blue-500 animate-pulse' : 'text-gray-400'}`} />
                    <p className="text-lg font-medium text-gray-900 mb-2">
                      {uploading ? 'Mengupload gambar...' : 'Upload Gambar Produk'}
                    </p>
                    <p className="text-gray-600 mb-2">
                      Klik untuk memilih gambar atau drag & drop
                    </p>
                    <p className="text-sm text-gray-500">
                      Format: JPG, PNG, WebP • Max 5MB per file
                    </p>
                  </label>
                </div>

                {/* Image Preview */}
                {form.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {form.images.map((image, index) => (
                      <div key={index} className="relative group">
                        <div className="relative h-32 bg-gray-100 rounded-lg overflow-hidden">
                          <Image
                            src={image}
                            alt={`Product image ${index + 1}`}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 w-6 h-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => removeImage(index)}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </form>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status */}
          <Card>
            <CardHeader>
              <CardTitle>Status Produk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="isActive">Status Aktif</Label>
                  <p className="text-sm text-gray-600">
                    Produk akan ditampilkan di toko
                  </p>
                </div>
                <Switch
                  id="isActive"
                  checked={form.isActive}
                  onCheckedChange={(checked) => setForm(prev => ({ ...prev, isActive: checked }))}
                />
              </div>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Preview Produk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="relative h-48 bg-gray-100 rounded-lg overflow-hidden">
                  {form.images.length > 0 ? (
                    <Image
                      src={form.images[0]}
                      alt="Product preview"
                      fill
                      className="object-cover"
                    />
                  ) : form.name ? (
                    <Image
                      src={getProductImage(form.name)}
                      alt="Product preview"
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Package className="w-12 h-12 text-gray-400" />
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold text-gray-900 line-clamp-2 min-h-[2.5rem]">
                    {form.name || 'Nama Produk'}
                  </h3>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-red-600">
                      {form.price > 0 ? formatCurrency(form.price) : 'Rp 0'}
                    </span>
                    <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      Stok: {form.stock} unit
                    </span>
                  </div>
                  {form.categoryId && (
                    <p className="text-sm text-gray-600">
                      Kategori: {categories.find(c => c.id === form.categoryId)?.name || 'Tidak dipilih'}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardContent className="p-6">
              <div className="space-y-3">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={loading}
                  onClick={(e) => {
                    e.preventDefault()
                    const form = document.querySelector('form') as HTMLFormElement
                    if (form) {
                      form.requestSubmit()
                    }
                  }}
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Menyimpan...' : 'Simpan Produk'}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  asChild
                >
                  <Link href="/admin/products">
                    Batal
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
