import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const categorySchema = z.object({
  name: z.string().min(1, '<PERSON><PERSON> kategori wajib diisi'),
  description: z.string().optional().nullable(),
  image: z.string().url().optional().or(z.literal('')).nullable(),
  isActive: z.boolean().default(true)
})

// GET /api/admin/categories - Get all categories for admin
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const includeProducts = searchParams.get('includeProducts') === 'true'

    const categories = await prisma.category.findMany({
      include: {
        _count: {
          select: {
            products: true
          }
        },
        ...(includeProducts && {
          products: {
            select: {
              id: true,
              name: true,
              price: true,
              stock: true,
              isActive: true
            }
          }
        })
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({
      success: true,
      data: categories
    })

  } catch (error) {
    console.error('Get admin categories error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/admin/categories - Create new category
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    console.log('Create category request body:', body)

    const data = categorySchema.parse(body)
    console.log('Parsed category data:', data)

    // Check if category name already exists
    const existingCategory = await prisma.category.findFirst({
      where: {
        name: data.name // MySQL is case-insensitive by default for VARCHAR
      }
    })

    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Kategori dengan nama ini sudah ada' },
        { status: 400 }
      )
    }

    const category = await prisma.category.create({
      data: {
        name: data.name,
        description: data.description || null,
        image: data.image || null,
        isActive: data.isActive ?? true
      },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Kategori berhasil ditambahkan',
      data: category
    })

  } catch (error) {
    console.error('Create category error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0]?.message || 'Data tidak valid', details: error.errors },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
