import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/messages/conversations - Get user conversations
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const isAdmin = session.user.role === 'ADMIN'

    let conversations

    if (isAdmin) {
      // Ad<PERSON> sees all conversations
      conversations = await prisma.conversation.findMany({
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 1,
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                  role: true
                }
              }
            }
          },
          _count: {
            select: {
              messages: {
                where: {
                  isRead: false,
                  senderId: { not: session.user.id }
                }
              }
            }
          }
        },
        orderBy: { updatedAt: 'desc' }
      })

      // Format for admin
      const formattedConversations = conversations.map(conv => ({
        id: conv.id,
        customerId: conv.customerId,
        customerName: conv.customer.name || conv.customer.email,
        lastMessage: conv.messages[0] ? {
          id: conv.messages[0].id,
          content: conv.messages[0].content,
          senderId: conv.messages[0].senderId,
          senderName: conv.messages[0].sender.name || 'Unknown',
          senderRole: conv.messages[0].sender.role,
          createdAt: conv.messages[0].createdAt.toISOString(),
          isRead: conv.messages[0].isRead
        } : null,
        unreadCount: conv._count.messages,
        status: conv.status,
        createdAt: conv.createdAt.toISOString(),
        updatedAt: conv.updatedAt.toISOString(),
        messages: [] // Will be loaded separately when conversation is selected
      }))

      return NextResponse.json({
        success: true,
        data: formattedConversations
      })

    } else {
      // Customer sees only their conversation
      const conversation = await prisma.conversation.findFirst({
        where: { customerId: session.user.id },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                  role: true
                }
              }
            }
          },
          _count: {
            select: {
              messages: {
                where: {
                  isRead: false,
                  senderId: { not: session.user.id }
                }
              }
            }
          }
        }
      })

      if (!conversation) {
        return NextResponse.json({
          success: true,
          data: []
        })
      }

      // Format for customer
      const formattedConversation = {
        id: conversation.id,
        customerId: conversation.customerId,
        customerName: session.user.name || session.user.email,
        lastMessage: conversation.messages.length > 0 ? {
          id: conversation.messages[conversation.messages.length - 1].id,
          content: conversation.messages[conversation.messages.length - 1].content,
          senderId: conversation.messages[conversation.messages.length - 1].senderId,
          senderName: conversation.messages[conversation.messages.length - 1].sender.name || 'Admin',
          senderRole: conversation.messages[conversation.messages.length - 1].sender.role,
          createdAt: conversation.messages[conversation.messages.length - 1].createdAt.toISOString(),
          isRead: conversation.messages[conversation.messages.length - 1].isRead
        } : null,
        unreadCount: conversation._count.messages,
        status: conversation.status,
        createdAt: conversation.createdAt.toISOString(),
        updatedAt: conversation.updatedAt.toISOString(),
        messages: conversation.messages.map(msg => ({
          id: msg.id,
          content: msg.content,
          senderId: msg.senderId,
          senderName: msg.sender.name || (msg.sender.role === 'ADMIN' ? 'Admin' : 'Customer'),
          senderRole: msg.sender.role,
          createdAt: msg.createdAt.toISOString(),
          isRead: msg.isRead
        }))
      }

      return NextResponse.json({
        success: true,
        data: [formattedConversation]
      })
    }

  } catch (error) {
    console.error('Get conversations error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/messages/conversations - Create new conversation (customer only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    if (session.user.role === 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admins cannot create conversations' },
        { status: 403 }
      )
    }

    // Check if conversation already exists
    let conversation = await prisma.conversation.findFirst({
      where: { customerId: session.user.id },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                role: true
              }
            }
          }
        }
      }
    })

    // Create new conversation if doesn't exist
    if (!conversation) {
      conversation = await prisma.conversation.create({
        data: {
          customerId: session.user.id,
          status: 'ACTIVE'
        },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                  role: true
                }
              }
            }
          }
        }
      })
    }

    // Format response
    const formattedConversation = {
      id: conversation.id,
      customerId: conversation.customerId,
      customerName: session.user.name || session.user.email,
      lastMessage: conversation.messages.length > 0 ? {
        id: conversation.messages[conversation.messages.length - 1].id,
        content: conversation.messages[conversation.messages.length - 1].content,
        senderId: conversation.messages[conversation.messages.length - 1].senderId,
        senderName: conversation.messages[conversation.messages.length - 1].sender.name || 'Admin',
        senderRole: conversation.messages[conversation.messages.length - 1].sender.role,
        createdAt: conversation.messages[conversation.messages.length - 1].createdAt.toISOString(),
        isRead: conversation.messages[conversation.messages.length - 1].isRead
      } : null,
      unreadCount: 0,
      status: conversation.status,
      createdAt: conversation.createdAt.toISOString(),
      updatedAt: conversation.updatedAt.toISOString(),
      messages: conversation.messages.map(msg => ({
        id: msg.id,
        content: msg.content,
        senderId: msg.senderId,
        senderName: msg.sender.name || (msg.sender.role === 'ADMIN' ? 'Admin' : 'Customer'),
        senderRole: msg.sender.role,
        createdAt: msg.createdAt.toISOString(),
        isRead: msg.isRead
      }))
    }

    return NextResponse.json({
      success: true,
      data: formattedConversation,
      message: conversation.messages.length === 0 ? 'Conversation created' : 'Conversation found'
    })

  } catch (error) {
    console.error('Create conversation error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
