import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const contentSchema = z.object({
  key: z.string().min(1, 'Key wajib diisi'),
  title: z.string().min(1, 'Judul wajib diisi'),
  content: z.string().min(1, 'Konten wajib diisi'),
  type: z.enum(['TEXT', 'HTML', 'IMAGE', 'JSON']),
  isActive: z.boolean().default(true)
})

// GET /api/admin/content - Get all content sections
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('includeInactive') === 'true'

    const contentSections = await prisma.contentSection.findMany({
      where: includeInactive ? {} : { isActive: true },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({
      success: true,
      data: contentSections
    })

  } catch (error) {
    console.error('Get content sections error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/admin/content - Create new content section
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const data = contentSchema.parse(body)

    // Check if key already exists
    const existingContent = await prisma.contentSection.findFirst({
      where: {
        key: data.key
      }
    })

    if (existingContent) {
      return NextResponse.json(
        { success: false, error: 'Key sudah digunakan' },
        { status: 400 }
      )
    }

    const contentSection = await prisma.contentSection.create({
      data: {
        key: data.key,
        title: data.title,
        content: data.content,
        type: data.type,
        isActive: data.isActive
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Konten berhasil ditambahkan',
      data: contentSection
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error('Create content section error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
