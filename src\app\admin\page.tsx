'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

import {
  TrendingUp,
  DollarSign,
  Users,
  Package,
  Eye,
  ShoppingCart,
  Star,
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface DashboardStats {
  totalRevenue: number
  revenueGrowth: number
  totalCustomers: number
  customerGrowth: number
  topProduct: {
    name: string
    salesCount: number
  }
}

interface ProductSalesData {
  name: string
  sales: number
  revenue: number
}

interface RecentOrder {
  id: string
  orderNumber: string
  customerName: string
  total: number
  status: string
  createdAt: string
}



export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [productSales, setProductSales] = useState<ProductSalesData[]>([])
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([])
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('7d')

  useEffect(() => {
    fetchDashboardData()
  }, [timeRange])

  const fetchDashboardData = async () => {
    setLoading(true)
    try {
      const [statsResponse, salesResponse, ordersResponse] = await Promise.all([
        fetch(`/api/admin/dashboard/stats?timeRange=${timeRange}`),
        fetch(`/api/admin/dashboard/product-sales?timeRange=${timeRange}`),
        fetch('/api/admin/dashboard/recent-orders?limit=5')
      ])

      const [statsResult, salesResult, ordersResult] = await Promise.all([
        statsResponse.json(),
        salesResponse.json(),
        ordersResponse.json()
      ])

      if (statsResult.success) {
        setStats(statsResult.data)
      }
      if (salesResult.success) {
        setProductSales(salesResult.data)
      }
      if (ordersResult.success) {
        setRecentOrders(ordersResult.data)
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'PENDING_PAYMENT': { label: 'Menunggu Bayar', variant: 'secondary' as const },
      'PAYMENT_VERIFIED': { label: 'Diverifikasi', variant: 'default' as const },
      'SHIPPED': { label: 'Dikirim', variant: 'default' as const },
      'DELIVERED': { label: 'Selesai', variant: 'default' as const },
      'CANCELLED': { label: 'Dibatalkan', variant: 'destructive' as const },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'secondary' as const }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }



  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Ringkasan insight cepat untuk admin toko Acikoo.</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 Hari</SelectItem>
              <SelectItem value="30d">30 Hari</SelectItem>
              <SelectItem value="90d">90 Hari</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" asChild>
            <a href="/" target="_blank" rel="noopener noreferrer">
              <Eye className="w-4 h-4 mr-2" />
              Lihat Toko
            </a>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Total Penjualan */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Penjualan</p>
                <p className="text-2xl font-bold">
                  {loading ? '...' : formatCurrency(stats?.totalRevenue || 0)}
                </p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                  <span className="text-sm text-green-600">
                    +{loading ? '...' : stats?.revenueGrowth || 0}%
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Customer */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Customer</p>
                <p className="text-2xl font-bold">
                  {loading ? '...' : stats?.totalCustomers || 0}
                </p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                  <span className="text-sm text-green-600">
                    +{loading ? '...' : stats?.customerGrowth || 0}%
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Produk Paling Laku */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Produk Paling Laku</p>
                <p className="text-2xl font-bold truncate">
                  {loading ? '...' : stats?.topProduct?.name || 'Belum ada data'}
                </p>
                <div className="flex items-center mt-1">
                  <Star className="w-4 h-4 text-orange-600 mr-1" />
                  <span className="text-sm text-orange-600">
                    {loading ? '...' : stats?.topProduct?.salesCount || 0} terjual
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Product Sales Chart */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Penjualan per Produk</CardTitle>
          <div className="text-sm text-gray-500">
            Filter: {timeRange === '7d' ? '7 Hari' : timeRange === '30d' ? '30 Hari' : '90 Hari'}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="h-[300px] flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-500">Memuat data penjualan...</p>
              </div>
            </div>
          ) : productSales.length > 0 ? (
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={productSales}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="name"
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip
                    formatter={(value, name) => [
                      name === 'sales' ? `${value} terjual` : formatCurrency(Number(value)),
                      name === 'sales' ? 'Jumlah Terjual' : 'Revenue'
                    ]}
                  />
                  <Bar dataKey="sales" fill="#3b82f6" name="sales" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-[300px] flex items-center justify-center text-gray-500">
              <div className="text-center">
                <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Belum ada data penjualan</p>
                <p className="text-sm">Data akan muncul setelah ada transaksi</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Orders Table */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center">
            <ShoppingCart className="w-5 h-5 text-blue-600 mr-2" />
            5 Pesanan Terakhir
          </CardTitle>
          <Button variant="outline" size="sm" asChild>
            <a href="/admin/orders">Lihat Semua</a>
          </Button>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="h-[200px] flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-500">Memuat pesanan...</p>
              </div>
            </div>
          ) : recentOrders.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-4 font-medium text-gray-600">No. Pesanan</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-600">Customer</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-600">Total</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-600">Status</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-600">Tanggal</th>
                  </tr>
                </thead>
                <tbody>
                  {recentOrders.map((order) => (
                    <tr key={order.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium">#{order.orderNumber}</td>
                      <td className="py-3 px-4">{order.customerName}</td>
                      <td className="py-3 px-4 font-medium">{formatCurrency(order.total)}</td>
                      <td className="py-3 px-4">{getStatusBadge(order.status)}</td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(order.createdAt).toLocaleDateString('id-ID')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="h-[200px] flex items-center justify-center text-gray-500">
              <div className="text-center">
                <ShoppingCart className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Belum ada pesanan</p>
                <p className="text-sm">Pesanan akan muncul setelah customer melakukan pembelian</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
