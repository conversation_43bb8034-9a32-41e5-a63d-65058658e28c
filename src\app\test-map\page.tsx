'use client'

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MapPin } from 'lucide-react'

// Dynamic import untuk DeliveryMap component
const DeliveryMap = dynamic(() => import('@/components/maps/delivery-map').then(mod => mod.DeliveryMap), { 
  ssr: false,
  loading: () => (
    <div className="h-64 rounded-lg overflow-hidden border bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <MapPin className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-600">Memuat peta...</p>
      </div>
    </div>
  )
})

export default function TestMapPage() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Store position (Pluit, Jakarta Utara)
  const storePosition: [number, number] = [-6.1289, 106.7889]

  // Customer position (Kelapa Gading)
  const customerPosition: [number, number] = [-6.1478, 106.8917]

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">🗺️ Test Peta Pengiriman</h1>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="w-5 h-5 mr-2 text-red-600" />
            Peta Test
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">
              <strong>Toko:</strong> Pluit, Jakarta Utara (-6.1289, 106.7889)
            </p>
            <p className="text-sm text-gray-600 mb-4">
              <strong>Tujuan:</strong> Kelapa Gading Mall (-6.1478, 106.8917)
            </p>
          </div>

          {isClient && (
            <DeliveryMap
              storePosition={storePosition}
              customerPosition={customerPosition}
              deliveryAddress="Kelapa Gading Mall, Jl. Boulevard Raya, Kelapa Gading, Jakarta Utara"
              orderNumber="TEST-MAP-001"
            />
          )}

          {/* Map Legend */}
          <div className="mt-3 flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span>Toko</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span>Tujuan</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-4 h-0.5 bg-red-500" style={{ borderStyle: 'dashed' }}></div>
                <span>Rute</span>
              </div>
            </div>
            <span className="text-xs">
              📍 Test Map
            </span>
          </div>
        </CardContent>
      </Card>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-medium text-blue-900 mb-2">ℹ️ Info Test</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Peta menggunakan OpenStreetMap + Leaflet</li>
          <li>• Dynamic import untuk menghindari SSR issues</li>
          <li>• Error boundary untuk handling error</li>
          <li>• Client-side rendering only</li>
        </ul>
      </div>
    </div>
  )
}
