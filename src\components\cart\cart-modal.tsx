'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { X, Plus, Minus, ShoppingBag, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useCartStore } from '@/store/cart'
import { formatCurrency } from '@/lib/utils'
import { getProductImage as getProductImageUtil } from '@/lib/product-images'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'
import Image from 'next/image'

export function CartModal() {
  const router = useRouter()
  const { data: session } = useSession()
  const {
    items,
    total,
    itemCount,
    isOpen,
    setIsOpen,
    updateQuantity,
    removeItem,
    clearCart
  } = useCartStore()

  const handleCheckout = () => {
    if (!session) {
      toast.error('Silakan login terlebih dahulu')
      router.push('/auth/signin')
      return
    }

    if (items.length === 0) {
      toast.error('Keranjang masih kosong')
      return
    }

    setIsOpen(false)
    router.push('/checkout')
  }

  // Use centralized image utility
  const getProductImage = (productName: string) => {
    return getProductImageUtil(productName)
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-[70] transition-opacity"
        onClick={() => setIsOpen(false)}
      />

      {/* Modal */}
      <div className="fixed right-0 top-0 h-full w-full max-w-md bg-white z-[75] transform transition-transform duration-300 ease-in-out shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
          <div className="flex items-center space-x-2">
            <ShoppingBag className="w-5 h-5 text-red-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              Keranjang ({itemCount})
            </h2>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(false)}
            className="p-1 hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full bg-white">
          {items.length === 0 ? (
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center">
                <ShoppingBag className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">Keranjang masih kosong</p>
                <Button
                  onClick={() => setIsOpen(false)}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Mulai Belanja
                </Button>
              </div>
            </div>
          ) : (
            <>
              {/* Items */}
              <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {items.map((item) => (
                  <div key={item.productId} className="flex items-start space-x-3 bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
                    {/* Product Image */}
                    <div className="w-14 h-14 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                      <Image
                        src={getProductImage(item.name)}
                        alt={item.name}
                        width={56}
                        height={56}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Product Info & Controls */}
                    <div className="flex-1 min-w-0">
                      {/* Product Name & Price */}
                      <div className="mb-2">
                        <h3 className="font-medium text-gray-900 text-sm truncate">
                          {item.name}
                        </h3>
                        <p className="text-sm text-red-600 font-semibold">
                          {formatCurrency(item.price)}
                        </p>
                      </div>

                      {/* Quantity Controls & Remove */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateQuantity(item.productId, item.quantity - 1)}
                            className="w-7 h-7 p-0 border-gray-300"
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="w-3 h-3" />
                          </Button>

                          <span className="w-8 text-center text-sm font-medium">
                            {item.quantity}
                          </span>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateQuantity(item.productId, item.quantity + 1)}
                            className="w-7 h-7 p-0 border-gray-300"
                            disabled={item.quantity >= item.stock}
                          >
                            <Plus className="w-3 h-3" />
                          </Button>
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-semibold text-gray-900">
                            {formatCurrency(item.price * item.quantity)}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeItem(item.productId)}
                            className="w-7 h-7 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Stock Info */}
                      <p className="text-xs text-gray-500 mt-1">
                        Stok: {item.stock}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Footer */}
              <div className="border-t border-gray-200 bg-gray-50 p-4 space-y-4">
                {/* Total */}
                <div className="bg-white rounded-lg p-3 border">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-semibold text-gray-900">
                      Subtotal:
                    </span>
                    <span className="text-xl font-bold text-red-600">
                      {formatCurrency(total)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    *Belum termasuk ongkos kirim
                  </p>
                </div>

                {/* Actions */}
                <div className="space-y-2">
                  <Button
                    onClick={handleCheckout}
                    className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-3 text-base"
                    disabled={items.length === 0}
                  >
                    Lanjut ke Pembayaran
                  </Button>

                  <Button
                    variant="outline"
                    onClick={clearCart}
                    className="w-full text-gray-600 hover:text-gray-700 border-gray-300"
                    disabled={items.length === 0}
                  >
                    Kosongkan Keranjang
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  )
}
