import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/dashboard/stats - Get dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '7d'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    let previousStartDate = new Date()

    switch (timeRange) {
      case '24h':
        startDate.setDate(now.getDate() - 1)
        previousStartDate.setDate(now.getDate() - 2)
        break
      case '7d':
        startDate.setDate(now.getDate() - 7)
        previousStartDate.setDate(now.getDate() - 14)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        previousStartDate.setDate(now.getDate() - 60)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        previousStartDate.setDate(now.getDate() - 180)
        break
      default:
        startDate.setDate(now.getDate() - 7)
        previousStartDate.setDate(now.getDate() - 14)
    }

    // Get current period stats
    const [
      totalCustomers,
      unreadMessages,
      lowStockProducts,
      previousCustomers,
      totalOrders,
      totalRevenue,
      previousRevenue
    ] = await Promise.all([
      // Current period customers (new registrations)
      prisma.user.count({
        where: {
          createdAt: { gte: startDate },
          role: 'CUSTOMER'
        }
      }),

      // Unread messages
      prisma.message.count({
        where: {
          isRead: false,
          sender: {
            role: 'CUSTOMER'
          }
        }
      }),

      // Low stock products (stock <= 10)
      prisma.product.count({
        where: {
          stock: { lte: 10 },
          isActive: true
        }
      }),

      // Previous period customers for comparison
      prisma.user.count({
        where: {
          createdAt: {
            gte: previousStartDate,
            lt: startDate
          },
          role: 'CUSTOMER'
        }
      }),

      // Total orders in current period
      prisma.order.count({
        where: {
          createdAt: { gte: startDate }
        }
      }),

      // Total revenue in current period
      prisma.order.aggregate({
        _sum: { total: true },
        where: {
          createdAt: { gte: startDate },
          status: { in: ['PAYMENT_VERIFIED', 'SHIPPED', 'DELIVERED'] }
        }
      }),

      // Previous period revenue for comparison
      prisma.order.aggregate({
        _sum: { total: true },
        where: {
          createdAt: {
            gte: previousStartDate,
            lt: startDate
          },
          status: { in: ['PAYMENT_VERIFIED', 'SHIPPED', 'DELIVERED'] }
        }
      })
    ])

    // Calculate growth percentages
    const customerGrowth = previousCustomers > 0
      ? ((totalCustomers - previousCustomers) / previousCustomers) * 100
      : totalCustomers > 0 ? 100 : 0

    const currentRevenue = Number(totalRevenue._sum.total || 0)
    const prevRevenue = Number(previousRevenue._sum.total || 0)
    const revenueGrowth = prevRevenue > 0
      ? ((currentRevenue - prevRevenue) / prevRevenue) * 100
      : currentRevenue > 0 ? 100 : 0

    // Get top selling product
    const topProductData = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        order: {
          createdAt: { gte: startDate },
          status: { in: ['PAYMENT_VERIFIED', 'SHIPPED', 'DELIVERED'] }
        }
      },
      _sum: {
        quantity: true
      },
      orderBy: {
        _sum: {
          quantity: 'desc'
        }
      },
      take: 1
    })

    let topProduct = null
    if (topProductData.length > 0) {
      const product = await prisma.product.findUnique({
        where: { id: topProductData[0].productId },
        select: { name: true }
      })

      topProduct = {
        name: product?.name || 'Unknown Product',
        salesCount: topProductData[0]._sum.quantity || 0
      }
    }

    // Get additional stats
    const [
      totalCustomersCount,
      totalProductsCount,
      activeProductsCount
    ] = await Promise.all([
      prisma.user.count({
        where: { role: 'CUSTOMER' }
      }),
      prisma.product.count(),
      prisma.product.count({
        where: { isActive: true }
      })
    ])

    return NextResponse.json({
      success: true,
      data: {
        // Main KPIs
        totalRevenue: currentRevenue,
        revenueGrowth: Math.round(revenueGrowth * 100) / 100,
        totalCustomers,
        customerGrowth: Math.round(customerGrowth * 100) / 100,
        totalOrders,
        unreadMessages,
        lowStockProducts,
        topProduct,

        // Additional stats
        totalCustomersCount,
        totalProductsCount,
        activeProductsCount,

        // Period info
        timeRange,
        startDate: startDate.toISOString(),
        endDate: now.toISOString()
      }
    })

  } catch (error) {
    console.error('Get dashboard stats error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
