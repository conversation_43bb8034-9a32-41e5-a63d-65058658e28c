'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { X, Filter } from 'lucide-react'
import { Category } from '@/types'
import { formatCurrency } from '@/lib/utils'

interface ProductFiltersProps {
  categories: Category[]
  selectedCategory: string
  onCategoryChange: (categoryId: string) => void
  onPriceRangeChange: (min: number, max: number) => void
}

export function ProductFilters({
  categories,
  selectedCategory,
  onCategoryChange,
  onPriceRangeChange,
}: ProductFiltersProps) {
  const [priceRange, setPriceRange] = useState([0, 1000000])
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [selectedRatings, setSelectedRatings] = useState<number[]>([])
  const [inStock, setInStock] = useState(false)

  // Mock brands data
  const brands = [
    { id: '1', name: 'Brand A', count: 15 },
    { id: '2', name: 'Brand B', count: 8 },
    { id: '3', name: 'Brand C', count: 12 },
    { id: '4', name: 'Brand D', count: 6 },
  ]

  const ratings = [5, 4, 3, 2, 1]

  const handlePriceRangeChange = (value: number[]) => {
    setPriceRange(value)
    onPriceRangeChange(value[0], value[1])
  }

  const handleBrandToggle = (brandId: string) => {
    setSelectedBrands(prev =>
      prev.includes(brandId)
        ? prev.filter(id => id !== brandId)
        : [...prev, brandId]
    )
  }

  const handleRatingToggle = (rating: number) => {
    setSelectedRatings(prev =>
      prev.includes(rating)
        ? prev.filter(r => r !== rating)
        : [...prev, rating]
    )
  }

  const clearAllFilters = () => {
    onCategoryChange('')
    setPriceRange([0, 1000000])
    setSelectedBrands([])
    setSelectedRatings([])
    setInStock(false)
    onPriceRangeChange(0, 1000000)
  }

  const activeFiltersCount = 
    (selectedCategory ? 1 : 0) +
    (priceRange[0] > 0 || priceRange[1] < 1000000 ? 1 : 0) +
    selectedBrands.length +
    selectedRatings.length +
    (inStock ? 1 : 0)

  return (
    <div className="space-y-6">
      {/* Filter Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Filter className="w-5 h-5" />
              Filter Produk
            </CardTitle>
            {activeFiltersCount > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {activeFiltersCount} filter aktif
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="w-4 h-4 mr-1" />
                  Reset
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Categories */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Kategori</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div
            className={`flex items-center space-x-2 p-2 rounded cursor-pointer hover:bg-gray-50 ${
              !selectedCategory ? 'bg-blue-50 text-blue-600' : ''
            }`}
            onClick={() => onCategoryChange('')}
          >
            <span className="flex-1">Semua Kategori</span>
          </div>
          {categories.map((category) => (
            <div
              key={category.id}
              className={`flex items-center space-x-2 p-2 rounded cursor-pointer hover:bg-gray-50 ${
                selectedCategory === category.id ? 'bg-blue-50 text-blue-600' : ''
              }`}
              onClick={() => onCategoryChange(category.id)}
            >
              <span className="flex-1">{category.name}</span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Price Range */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Rentang Harga</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="px-2">
            <Slider
              value={priceRange}
              onValueChange={handlePriceRangeChange}
              max={1000000}
              step={10000}
              className="w-full"
            />
          </div>
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{formatCurrency(priceRange[0])}</span>
            <span>{formatCurrency(priceRange[1])}</span>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="minPrice" className="text-xs">Min</Label>
              <Input
                id="minPrice"
                type="number"
                value={priceRange[0]}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0
                  setPriceRange([value, priceRange[1]])
                }}
                className="h-8"
              />
            </div>
            <div>
              <Label htmlFor="maxPrice" className="text-xs">Max</Label>
              <Input
                id="maxPrice"
                type="number"
                value={priceRange[1]}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 1000000
                  setPriceRange([priceRange[0], value])
                }}
                className="h-8"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Brands */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Brand</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {brands.map((brand) => (
            <div key={brand.id} className="flex items-center space-x-2">
              <Checkbox
                id={`brand-${brand.id}`}
                checked={selectedBrands.includes(brand.id)}
                onCheckedChange={() => handleBrandToggle(brand.id)}
              />
              <Label
                htmlFor={`brand-${brand.id}`}
                className="flex-1 text-sm cursor-pointer"
              >
                {brand.name}
              </Label>
              <span className="text-xs text-gray-500">({brand.count})</span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Ratings */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Rating</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {ratings.map((rating) => (
            <div key={rating} className="flex items-center space-x-2">
              <Checkbox
                id={`rating-${rating}`}
                checked={selectedRatings.includes(rating)}
                onCheckedChange={() => handleRatingToggle(rating)}
              />
              <Label
                htmlFor={`rating-${rating}`}
                className="flex-1 text-sm cursor-pointer flex items-center"
              >
                <div className="flex items-center">
                  {Array.from({ length: 5 }, (_, i) => (
                    <span
                      key={i}
                      className={`text-sm ${
                        i < rating ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                    >
                      ★
                    </span>
                  ))}
                  <span className="ml-1">& ke atas</span>
                </div>
              </Label>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Stock Status */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Ketersediaan</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="inStock"
              checked={inStock}
              onCheckedChange={setInStock}
            />
            <Label htmlFor="inStock" className="text-sm cursor-pointer">
              Hanya yang tersedia
            </Label>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
