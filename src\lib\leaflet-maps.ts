// OpenStreetMap + Leaflet implementation for Acikoo
import { STORE_CONFIG } from './store-config'

export interface Coordinates {
  lat: number
  lng: number
}

export interface DeliveryZone {
  center: Coordinates
  radius: number // in kilometers
  fee: number
  color: string
  opacity: number
}

// Store location from centralized config
export const STORE_LOCATION: Coordinates = {
  lat: STORE_CONFIG.location.latitude,
  lng: STORE_CONFIG.location.longitude
}

// Delivery zones with updated pricing structure
export const DELIVERY_ZONES: DeliveryZone[] = [
  {
    center: STORE_LOCATION,
    radius: 4000, // 0-4km in meters for Leaflet
    fee: 10000, // Rp 10,000
    color: '#22c55e', // green
    opacity: 0.2
  },
  {
    center: STORE_LOCATION,
    radius: 5000, // 5km in meters
    fee: 13000, // Rp 13,000 (10k + 3k)
    color: '#3b82f6', // blue
    opacity: 0.2
  },
  {
    center: STORE_LOCATION,
    radius: 6000, // 6km in meters
    fee: 16000, // Rp 16,000 (10k + 6k)
    color: '#f59e0b', // yellow
    opacity: 0.2
  },
  {
    center: STORE_LOCATION,
    radius: 7000, // 7km in meters
    fee: 19000, // Rp 19,000 (10k + 9k)
    color: '#f97316', // orange
    opacity: 0.2
  },
  {
    center: STORE_LOCATION,
    radius: 8000, // 8km in meters
    fee: 22000, // Rp 22,000 (10k + 12k)
    color: '#ef4444', // red
    opacity: 0.2
  },
  {
    center: STORE_LOCATION,
    radius: 9000, // 9km in meters (max delivery)
    fee: 25000, // Rp 25,000 (10k + 15k)
    color: '#dc2626', // dark red
    opacity: 0.2
  }
]

/**
 * Calculate distance between two coordinates using Haversine formula
 */
export function calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
  const R = 6371 // Earth's radius in kilometers
  const dLat = toRadians(coord2.lat - coord1.lat)
  const dLng = toRadians(coord2.lng - coord1.lng)

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(coord1.lat)) * Math.cos(toRadians(coord2.lat)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const distance = R * c

  return distance
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

/**
 * Check if coordinates are within delivery radius
 */
export function isWithinDeliveryRadius(coordinates: Coordinates): boolean {
  const distance = calculateDistance(STORE_LOCATION, coordinates)
  const maxRadius = Math.max(...DELIVERY_ZONES.map(zone => zone.radius / 1000)) // Convert to km
  return distance <= maxRadius
}

/**
 * Calculate delivery fee based on distance
 */
export function calculateDeliveryFee(coordinates: Coordinates): number {
  const distance = calculateDistance(STORE_LOCATION, coordinates)

  // Find the appropriate delivery zone (convert radius from meters to km)
  // Sort zones by radius to check smallest first
  const sortedZones = [...DELIVERY_ZONES].sort((a, b) => a.radius - b.radius)

  for (const zone of sortedZones) {
    if (distance <= zone.radius / 1000) {
      return zone.fee
    }
  }

  // If outside all zones, return -1 to indicate no delivery
  return -1
}

/**
 * Get which delivery zone the coordinates belong to
 */
export function getDeliveryZone(coordinates: Coordinates): number {
  const distance = calculateDistance(STORE_LOCATION, coordinates)

  // Sort zones by radius to check smallest first
  const sortedZones = [...DELIVERY_ZONES].sort((a, b) => a.radius - b.radius)

  for (let i = 0; i < sortedZones.length; i++) {
    if (distance <= sortedZones[i].radius / 1000) {
      return i + 1 // Return zone number (1, 2, 3)
    }
  }

  return -1 // Outside all zones
}

/**
 * Get delivery info for coordinates
 */
export function getDeliveryInfo(coordinates: Coordinates) {
  const distance = calculateDistance(STORE_LOCATION, coordinates)
  const fee = calculateDeliveryFee(coordinates)
  const zone = getDeliveryZone(coordinates)
  const isDeliverable = fee !== -1

  // Estimate delivery time based on distance
  const estimatedMinutes = Math.ceil(distance * 10) + 30 // 10 min per km + 30 min prep

  return {
    distance: Math.round(distance * 100) / 100, // Round to 2 decimal places
    fee,
    zone,
    isDeliverable,
    estimatedMinutes,
    estimatedTime: `${Math.floor(estimatedMinutes / 60)}h ${estimatedMinutes % 60}m`
  }
}

// Rate limiting for Nominatim API (max 1 request per second)
let lastGeocodingRequest = 0

/**
 * Geocode address using Nominatim (OpenStreetMap)
 */
export async function geocodeAddress(address: string): Promise<Coordinates | null> {
  try {
    // Rate limiting: ensure at least 1 second between requests
    const now = Date.now()
    const timeSinceLastRequest = now - lastGeocodingRequest
    if (timeSinceLastRequest < 1000) {
      await new Promise(resolve => setTimeout(resolve, 1000 - timeSinceLastRequest))
    }
    lastGeocodingRequest = Date.now()

    // Add bias towards Jakarta, Indonesia for better results
    const query = encodeURIComponent(`${address}, Jakarta, Indonesia`)
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${query}&limit=1&countrycodes=id&addressdetails=1`,
      {
        headers: {
          'User-Agent': 'Acikoo-Store/1.0 (<EMAIL>)' // Required by Nominatim usage policy
        }
      }
    )

    if (!response.ok) {
      throw new Error('Geocoding request failed')
    }

    const data = await response.json()

    if (data && data.length > 0) {
      return {
        lat: parseFloat(data[0].lat),
        lng: parseFloat(data[0].lon)
      }
    }

    return null
  } catch (error) {
    console.error('Geocoding error:', error)
    return null
  }
}

/**
 * Reverse geocode coordinates to address using Nominatim
 */
export async function reverseGeocode(coordinates: Coordinates): Promise<string | null> {
  try {
    // Rate limiting: ensure at least 1 second between requests
    const now = Date.now()
    const timeSinceLastRequest = now - lastGeocodingRequest
    if (timeSinceLastRequest < 1000) {
      await new Promise(resolve => setTimeout(resolve, 1000 - timeSinceLastRequest))
    }
    lastGeocodingRequest = Date.now()

    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${coordinates.lat}&lon=${coordinates.lng}&addressdetails=1`,
      {
        headers: {
          'User-Agent': 'Acikoo-Store/1.0 (<EMAIL>)' // Required by Nominatim usage policy
        }
      }
    )

    if (!response.ok) {
      throw new Error('Reverse geocoding request failed')
    }

    const data = await response.json()

    if (data && data.display_name) {
      return data.display_name
    }

    return null
  } catch (error) {
    console.error('Reverse geocoding error:', error)
    return null
  }
}

/**
 * Get current user location using browser geolocation
 */
export function getCurrentLocation(): Promise<Coordinates> {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'))
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          lat: position.coords.latitude,
          lng: position.coords.longitude
        })
      },
      (error) => {
        reject(error)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    )
  })
}

/**
 * Load Leaflet CSS and JS dynamically
 */
export function loadLeafletAssets(): Promise<void> {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    if (typeof window !== 'undefined' && window.L) {
      resolve()
      return
    }

    // Load CSS
    const cssLink = document.createElement('link')
    cssLink.rel = 'stylesheet'
    cssLink.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'
    cssLink.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY='
    cssLink.crossOrigin = ''
    document.head.appendChild(cssLink)

    // Load JS
    const script = document.createElement('script')
    script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'
    script.integrity = 'sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo='
    script.crossOrigin = ''

    script.onload = () => resolve()
    script.onerror = () => reject(new Error('Failed to load Leaflet'))

    document.head.appendChild(script)
  })
}

// Tile layer options for different map styles
export const TILE_LAYERS = {
  openstreetmap: {
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
  },
  cartodb: {
    url: 'https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png',
    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>'
  }
}
