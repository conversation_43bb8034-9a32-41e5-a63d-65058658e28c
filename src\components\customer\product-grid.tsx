'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Star, Filter, ShoppingCart } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { ProductWithCategory } from '@/types'
import { useCartStore } from '@/store/cart'
import { toast } from 'sonner'

// Use centralized image utility
import { getProductImage } from '@/lib/product-images'

export function ProductGrid() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [products, setProducts] = useState<ProductWithCategory[]>([])
  const [loading, setLoading] = useState(true)
  const { addItem } = useCartStore()

  // Fetch products from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch('/api/products')
        const data = await response.json()
        if (data.success) {
          setProducts(data.data)
        }
      } catch (error) {
        console.error('Failed to fetch products:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  const categories = ['all', 'Menu Utama', 'Menu Spesial']

  const filteredProducts = selectedCategory === 'all'
    ? products
    : products.filter(product => product.category?.name === selectedCategory)

  const handleAddToCart = (product: ProductWithCategory) => {
    if (product.stock === 0) {
      toast.error('Produk sedang habis')
      return
    }

    addItem({
      id: product.id,
      productId: product.id,
      name: product.name,
      price: Number(product.price),
      image: getProductImage(product.name),
      stock: product.stock,
    })

    toast.success('Ditambahkan ke keranjang!', {
      description: `${product.name} berhasil ditambahkan`
    })
  }

  return (
    <section className="space-y-8">
      {/* Section Header */}
      <div className="text-center space-y-4">
        <div className="inline-block mb-4">
          <Badge className="bg-red-100 text-red-800 px-4 py-2 text-sm font-semibold">
            🌶️ Menu Spesial Kami
          </Badge>
        </div>
        <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
          Jajanan Aci yang <span className="text-red-600">Bikin Nagih!</span>
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          4 menu spesial berbahan dasar tepung aci dengan bumbu rahasia yang pedasnya pas banget!
          <span className="text-red-600 font-semibold">Dijamin ketagihan!</span>
        </p>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap justify-center gap-2">
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category)}
            className="capitalize"
          >
            {category === 'all' ? 'Semua' : category}
          </Button>
        ))}
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="aspect-[4/3] bg-gray-200 rounded-lg mb-4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          filteredProducts.map((product) => (
            <Card key={product.id} className="group hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-4">
                {/* Product Image */}
                <div className="relative aspect-[4/3] mb-4 bg-gradient-to-br from-red-100 to-orange-100 rounded-lg overflow-hidden">
                  <Image
                    src={getProductImage(product.name)}
                    alt={product.name}
                    width={300}
                    height={225}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/5"></div>

                  {/* Stock badge */}
                  {product.stock < 10 && product.stock > 0 && (
                    <Badge variant="destructive" className="absolute top-2 right-2 shadow-lg">
                      Stok Terbatas
                    </Badge>
                  )}
                  {product.stock === 0 && (
                    <Badge variant="outline" className="absolute top-2 right-2 shadow-lg bg-white">
                      Stok Habis
                    </Badge>
                  )}
                </div>

                {/* Product Info */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary" className="text-xs">
                      {product.category?.name || 'Menu'}
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm text-gray-600">4.8</span>
                    </div>
                  </div>

                  <h3 className="font-semibold text-gray-900 line-clamp-2">
                    {product.name}
                  </h3>

                  <p className="text-sm text-gray-600 line-clamp-2">
                    {product.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-red-600">
                      {formatCurrency(Number(product.price))}
                    </span>
                    <span className="text-sm text-gray-500">
                      Stok: {product.stock}
                    </span>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="p-4 pt-0">
                <Button
                  className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold"
                  onClick={() => handleAddToCart(product)}
                  disabled={product.stock === 0}
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  {product.stock === 0 ? 'Stok Habis' : '🌶️ Tambah ke Keranjang'}
                </Button>
              </CardFooter>
            </Card>
          ))
        )}
      </div>
    </section>
  )
}
