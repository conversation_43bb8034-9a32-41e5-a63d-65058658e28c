import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateContentSchema = z.object({
  key: z.string().min(1, 'Key wajib diisi').optional(),
  title: z.string().min(1, 'Judul wajib diisi').optional(),
  content: z.string().min(1, 'Konten wajib diisi').optional(),
  type: z.enum(['TEXT', 'HTML', 'IMAGE', 'JSON']).optional(),
  isActive: z.boolean().optional()
})

// GET /api/admin/content/[id] - Get content section detail
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: contentId } = await params

    const contentSection = await prisma.contentSection.findUnique({
      where: { id: contentId }
    })

    if (!contentSection) {
      return NextResponse.json(
        { success: false, error: 'Konten tidak ditemukan' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: contentSection
    })

  } catch (error) {
    console.error('Get content section detail error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/admin/content/[id] - Update content section
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: contentId } = await params
    const body = await request.json()
    const data = updateContentSchema.parse(body)

    // Check if content exists
    const existingContent = await prisma.contentSection.findUnique({
      where: { id: contentId }
    })

    if (!existingContent) {
      return NextResponse.json(
        { success: false, error: 'Konten tidak ditemukan' },
        { status: 404 }
      )
    }

    // Check if key already exists (if key is being updated)
    if (data.key && data.key !== existingContent.key) {
      const duplicateContent = await prisma.contentSection.findFirst({
        where: {
          key: data.key,
          id: {
            not: contentId
          }
        }
      })

      if (duplicateContent) {
        return NextResponse.json(
          { success: false, error: 'Key sudah digunakan' },
          { status: 400 }
        )
      }
    }

    const updatedContent = await prisma.contentSection.update({
      where: { id: contentId },
      data: {
        ...(data.key && { key: data.key }),
        ...(data.title && { title: data.title }),
        ...(data.content && { content: data.content }),
        ...(data.type && { type: data.type }),
        ...(data.isActive !== undefined && { isActive: data.isActive })
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Konten berhasil diperbarui',
      data: updatedContent
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error('Update content section error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/content/[id] - Delete content section
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: contentId } = await params

    // Check if content exists
    const existingContent = await prisma.contentSection.findUnique({
      where: { id: contentId }
    })

    if (!existingContent) {
      return NextResponse.json(
        { success: false, error: 'Konten tidak ditemukan' },
        { status: 404 }
      )
    }

    await prisma.contentSection.delete({
      where: { id: contentId }
    })

    return NextResponse.json({
      success: true,
      message: 'Konten berhasil dihapus'
    })

  } catch (error) {
    console.error('Delete content section error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
