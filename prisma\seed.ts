import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Ad<PERSON>ko<PERSON>',
      password: adminPassword,
      role: 'ADMIN',
      phone: '+6281234567890',
      address: 'Jl. Admin No. 1, Jakarta',
    },
  })

  // Create customer user
  const customerPassword = await bcrypt.hash('customer123', 12)
  const customer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Customer Demo',
      password: customerPassword,
      role: 'CUSTOMER',
      phone: '+6281234567891',
      address: 'Jl. Customer No. 1, Jakarta',
      latitude: -6.2088,
      longitude: 106.8456,
    },
  })

  console.log('✅ Users created')

  // Create categories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { id: 'cat-1' },
      update: {},
      create: {
        id: 'cat-1',
        name: 'Menu Utama',
        description: 'Jajanan aci pedas yang bikin nagih!',
        image: '/categories/menu-utama.jpg',
      },
    }),
    prisma.category.upsert({
      where: { id: 'cat-2' },
      update: {},
      create: {
        id: 'cat-2',
        name: 'Menu Spesial',
        description: 'Varian premium dengan topping istimewa',
        image: '/categories/menu-spesial.jpg',
      },
    }),
  ])

  console.log('✅ Categories created')

  // Create products - 4 Menu Spesial Acikoo
  const products = [
    {
      name: 'Cipak Koceak',
      description: '🔥 Menu andalan yang bikin ketagihan! Aci kenyal dengan bumbu rahasia yang pedasnya pas banget. Cocok buat yang suka tantangan rasa!',
      price: 10000,
      categoryId: 'cat-1',
      stock: 50,
      weight: 150,
    },
    {
      name: 'Cipak Mozarella',
      description: '🧀 Level up dari Cipak Koceak! Ditambah keju mozarella yang meleleh di mulut. Perpaduan pedas dan creamy yang gak ada duanya!',
      price: 13000,
      categoryId: 'cat-2',
      stock: 40,
      weight: 180,
    },
    {
      name: 'Cirambay',
      description: '🌶️ Aci rambay yang crispy di luar, kenyal di dalam! Bumbu pedasnya meresap sempurna. Sekali gigit, langsung jatuh cinta!',
      price: 12000,
      categoryId: 'cat-1',
      stock: 45,
      weight: 160,
    },
    {
      name: 'Basreng',
      description: '🔥 Bakso goreng aci yang crispy abis! Dibalur bumbu pedas manis yang bikin nagih. Perfect buat ngemil sambil ngobrol!',
      price: 13000,
      categoryId: 'cat-2',
      stock: 35,
      weight: 170,
    },
  ]

  for (const product of products) {
    const existingProduct = await prisma.product.findFirst({
      where: { name: product.name }
    })

    if (!existingProduct) {
      await prisma.product.create({
        data: product
      })
    }
  }

  console.log('✅ Products created')

  // Create content sections
  const contentSections = [
    {
      key: 'hero_title',
      title: 'Hero Title',
      content: 'Jajanan Aci Pedas yang Bikin Nagih!',
      type: 'text',
    },
    {
      key: 'hero_subtitle',
      title: 'Hero Subtitle',
      content: 'Rasain sensasi pedas yang bikin ketagihan! Order sekarang dan nikmatin jajanan aci terenak se-kota. Dijamin bikin kamu balik lagi! 🔥',
      type: 'text',
    },
    {
      key: 'store_name',
      title: 'Store Name',
      content: 'Acikoo - Jajanan Aci Pedas',
      type: 'text',
    },
    {
      key: 'store_address',
      title: 'Store Address',
      content: 'Jl. Raya Cipayung No. 88, Kelurahan Cipayung, Kecamatan Cipayung, Jakarta Timur 13840',
      type: 'text',
    },
    {
      key: 'store_phone',
      title: 'Store Phone',
      content: '+62 812-3456-7890',
      type: 'text',
    },
    {
      key: 'store_email',
      title: 'Store Email',
      content: '<EMAIL>',
      type: 'text',
    },
    {
      key: 'about_title',
      title: 'About Title',
      content: 'Apa Sih Acikoo Itu?',
      type: 'text',
    },
    {
      key: 'about_description',
      title: 'About Description',
      content: 'Acikoo adalah brand jajanan aci pedas yang lahir dari kecintaan terhadap cita rasa Indonesia. Kami menghadirkan jajanan berbahan dasar tepung aci dengan bumbu rahasia yang bikin nagih. Setiap gigitan adalah perpaduan sempurna antara tekstur kenyal dan rasa pedas yang memanjakan lidah!',
      type: 'text',
    },
    {
      key: 'store_hours',
      title: 'Store Hours',
      content: JSON.stringify({
        monday: { open: '10:00', close: '22:00', isOpen: true },
        tuesday: { open: '10:00', close: '22:00', isOpen: true },
        wednesday: { open: '10:00', close: '22:00', isOpen: true },
        thursday: { open: '10:00', close: '22:00', isOpen: true },
        friday: { open: '10:00', close: '23:00', isOpen: true },
        saturday: { open: '10:00', close: '23:00', isOpen: true },
        sunday: { open: '12:00', close: '22:00', isOpen: true },
      }),
      type: 'json',
    },
  ]

  for (const section of contentSections) {
    await prisma.contentSection.upsert({
      where: { key: section.key },
      update: {},
      create: section,
    })
  }

  console.log('✅ Content sections created')
  console.log('🎉 Database seeding completed!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
