import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/products - Get all products for admin with advanced filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const categoryId = searchParams.get('categoryId')
    const isActive = searchParams.get('isActive')
    const stockFilter = searchParams.get('stockFilter')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    // Search functionality
    if (search) {
      where.OR = [
        { name: { contains: search } }, // MySQL is case-insensitive by default
        { description: { contains: search } },
        { category: { name: { contains: search } } }
      ]
    }

    // Category filter
    if (categoryId) {
      where.categoryId = categoryId
    }

    // Active status filter
    if (isActive !== null && isActive !== '') {
      where.isActive = isActive === 'true'
    }

    // Stock filter
    if (stockFilter) {
      switch (stockFilter) {
        case 'out':
          where.stock = 0
          break
        case 'low':
          where.stock = { gt: 0, lte: 10 }
          break
        case 'available':
          where.stock = { gt: 10 }
          break
      }
    }

    // Build orderBy
    const orderBy: any = {}
    orderBy[sortBy] = sortOrder

    const [products, total, categories] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              orderItems: true
            }
          }
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.product.count({ where }),
      prisma.category.findMany({
        select: {
          id: true,
          name: true,
          _count: {
            select: {
              products: true
            }
          }
        },
        orderBy: { name: 'asc' }
      })
    ])

    // Format products with parsed images
    const formattedProducts = products.map(product => ({
      ...product,
      images: product.images ? JSON.parse(product.images) : [],
      orderCount: product._count.orderItems
    }))

    return NextResponse.json({
      success: true,
      data: {
        products: formattedProducts,
        categories,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        stats: {
          totalProducts: total,
          activeProducts: await prisma.product.count({ where: { isActive: true } }),
          inactiveProducts: await prisma.product.count({ where: { isActive: false } }),
          outOfStock: await prisma.product.count({ where: { stock: 0 } }),
          lowStock: await prisma.product.count({ where: { stock: { gt: 0, lte: 10 } } })
        }
      }
    })

  } catch (error) {
    console.error('Get admin products error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
