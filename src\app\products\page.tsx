'use client'

import { useState, useEffect } from 'react'
import { Navbar } from '@/components/layout/navbar'
import { Footer } from '@/components/layout/footer'
import { ProductCard } from '@/components/customer/product-card'
import { ProductFilters } from '@/components/customer/product-filters'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, Grid, List, Filter } from 'lucide-react'
import { ProductWithCategory, Category } from '@/types'

export default function ProductsPage() {
  const [products, setProducts] = useState<ProductWithCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch products
  const fetchProducts = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)

      const response = await fetch(`/api/products?${params}`)
      const data = await response.json()

      if (data.success) {
        setProducts(data.data)
      }
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProducts()
  }, [searchTerm])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchProducts()
  }

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="inline-block mb-4">
            <span className="bg-red-100 text-red-800 px-4 py-2 rounded-full text-sm font-bold">
              🌶️ MENU SPESIAL ACIKOO 🌶️
            </span>
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Menu Aci Pedas
            <span className="block bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
              Terenak Se-Kota!
            </span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Pilih jajanan aci pedas favorit kamu! Semua dibuat dengan resep rahasia dan bumbu pilihan yang bikin nagih.
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-12">
          <div className="max-w-md mx-auto">
            <form onSubmit={handleSearch} className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Cari menu aci pedas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 pr-4 py-3 text-lg border-2 border-red-200 focus:border-red-500 rounded-full"
              />
            </form>
            {searchTerm && (
              <p className="text-center text-gray-600 mt-3">
                Menampilkan hasil untuk "{searchTerm}"
              </p>
            )}
          </div>
        </div>

        {/* Products Grid */}
        <div className="max-w-6xl mx-auto">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="bg-gray-200 aspect-square rounded-2xl mb-4"></div>
                  <div className="space-y-3">
                    <div className="h-6 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredProducts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {filteredProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  viewMode="grid"
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
                <Search className="w-12 h-12 text-red-400" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                {searchTerm ? 'Menu tidak ditemukan' : 'Belum ada menu'}
              </h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                {searchTerm
                  ? `Tidak ada menu yang cocok dengan "${searchTerm}". Coba kata kunci lain!`
                  : 'Menu aci pedas spesial sedang dalam persiapan. Stay tuned!'
                }
              </p>
              {searchTerm && (
                <Button
                  onClick={() => setSearchTerm('')}
                  className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
                >
                  Lihat Semua Menu
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Call to Action */}
        {!loading && filteredProducts.length > 0 && (
          <div className="text-center mt-16 p-8 bg-gradient-to-r from-red-50 to-orange-50 rounded-2xl">
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              Belum Menemukan yang Cocok?
            </h3>
            <p className="text-gray-600 mb-6">
              Hubungi kami untuk rekomendasi menu atau request level kepedasan khusus!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                asChild
              >
                <a href="https://wa.me/6285282305679" target="_blank" rel="noopener noreferrer">
                  💬 Chat WhatsApp
                </a>
              </Button>
              {/* <Button
                size="lg"
                variant="outline"
                className="border-red-300 text-red-600 hover:bg-red-50"
                asChild
              >
                <a href="/recipes">
                  👨‍🍳 Lihat Resep Rahasia
                </a>
              </Button> */}
            </div>
          </div>
        )}
      </main>

      <Footer />
    </div>
  )
}
