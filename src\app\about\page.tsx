'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Navbar } from '@/components/layout/navbar'
import { Footer } from '@/components/layout/footer'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  MapPin,
  Clock,
  Phone,
  Mail,
  Star,
  Users,
  Award,
  Heart,
  ChefHat,
  Truck,
  Shield,
  Zap
} from 'lucide-react'
import { STORE_CONFIG } from '@/lib/store-config'

interface AboutContent {
  heroTitle: string
  heroSubtitle: string
  heroDescription: string
  storyTitle: string
  storyContent: string
  missionTitle: string
  missionContent: string
  visionTitle: string
  visionContent: string
  valuesTitle: string
  values: Array<{
    icon: string
    title: string
    description: string
  }>
}

export default function AboutPage() {
  const [aboutContent, setAboutContent] = useState<AboutContent | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAboutContent()
  }, [])

  const fetchAboutContent = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/content/about')
      const result = await response.json()

      if (response.ok && result.success) {
        setAboutContent(result.data)
      } else {
        // Fallback to default content
        setAboutContent(getDefaultContent())
      }
    } catch (error) {
      console.error('Error fetching about content:', error)
      setAboutContent(getDefaultContent())
    } finally {
      setLoading(false)
    }
  }

  const getDefaultContent = (): AboutContent => ({
    heroTitle: "Tentang Acikoo",
    heroSubtitle: "Aci Pedas Terenak di Jakarta Timur",
    heroDescription: "Sejak 2020, Acikoo telah menjadi destinasi favorit untuk jajanan aci pedas yang autentik dan menggugah selera. Dengan resep rahasia turun temurun dan bahan-bahan pilihan, kami menghadirkan cita rasa yang tak terlupakan.",
    storyTitle: "Cerita Kami",
    storyContent: "Acikoo lahir dari kecintaan terhadap jajanan tradisional Indonesia, khususnya aci pedas. Dimulai dari warung kecil di Cipayung, Jakarta Timur, kami berkomitmen untuk menghadirkan aci pedas dengan kualitas terbaik dan cita rasa yang konsisten. Setiap porsi dibuat dengan penuh cinta dan perhatian detail.",
    missionTitle: "Misi Kami",
    missionContent: "Menghadirkan jajanan aci pedas berkualitas tinggi dengan pelayanan terbaik, sambil melestarikan cita rasa tradisional Indonesia untuk generasi mendatang.",
    visionTitle: "Visi Kami",
    visionContent: "Menjadi brand aci pedas terdepan di Indonesia yang dikenal karena kualitas, inovasi, dan pelayanan yang memuaskan pelanggan.",
    valuesTitle: "Nilai-Nilai Kami",
    values: [
      {
        icon: "quality",
        title: "Kualitas Terjamin",
        description: "Menggunakan bahan-bahan pilihan dan proses pembuatan yang higienis"
      },
      {
        icon: "service",
        title: "Pelayanan Prima",
        description: "Memberikan pengalaman berbelanja yang menyenangkan dan memuaskan"
      },
      {
        icon: "innovation",
        title: "Inovasi Berkelanjutan",
        description: "Terus berinovasi dalam rasa dan pelayanan untuk kepuasan pelanggan"
      },
      {
        icon: "trust",
        title: "Kepercayaan",
        description: "Membangun hubungan jangka panjang berdasarkan kepercayaan dan kualitas"
      }
    ]
  })

  const getValueIcon = (iconType: string) => {
    const icons = {
      quality: <Award className="w-8 h-8 text-red-600" />,
      service: <Heart className="w-8 h-8 text-red-600" />,
      innovation: <Zap className="w-8 h-8 text-red-600" />,
      trust: <Shield className="w-8 h-8 text-red-600" />
    }
    return icons[iconType as keyof typeof icons] || <Star className="w-8 h-8 text-red-600" />
  }

  const stats = [
    { icon: <Users className="w-8 h-8 text-red-600" />, value: "10,000+", label: "Pelanggan Puas" },
    { icon: <ChefHat className="w-8 h-8 text-red-600" />, value: "50+", label: "Varian Rasa" },
    { icon: <Truck className="w-8 h-8 text-red-600" />, value: "1,000+", label: "Pesanan Terkirim" },
    { icon: <Star className="w-8 h-8 text-red-600" />, value: "4.8/5", label: "Rating Pelanggan" }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50">
        <Navbar />
        <main className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-8">
            <div className="h-64 bg-gray-200 rounded-lg"></div>
            <div className="space-y-4">
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50">
      <Navbar />

      <main>
        {/* Hero Section */}
        <section className="relative py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-red-600/10 to-orange-600/10"></div>
          <div className="container mx-auto px-4 relative">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-block mb-6">
                <span className="bg-red-100 text-red-800 px-4 py-2 rounded-full text-sm font-bold">
                  🌶️ TENTANG ACIKOO 🌶️
                </span>
              </div>
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
                {aboutContent?.heroTitle}
              </h1>
              <p className="text-xl lg:text-2xl text-red-600 font-semibold mb-6">
                {aboutContent?.heroSubtitle}
              </p>
              <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
                {aboutContent?.heroDescription}
              </p>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="flex justify-center mb-4">
                    {stat.icon}
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">
                    {aboutContent?.storyTitle}
                  </h2>
                  <p className="text-gray-700 leading-relaxed text-lg">
                    {aboutContent?.storyContent}
                  </p>
                </div>
                <div className="relative h-80 bg-gray-100 rounded-lg overflow-hidden">
                  <Image
                    src="/images/about-story.jpg"
                    alt="Cerita Acikoo"
                    fill
                    className="object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.style.display = 'none'
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12">
                <Card className="border-2 border-red-100">
                  <CardContent className="p-8">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Heart className="w-8 h-8 text-red-600" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900">
                        {aboutContent?.missionTitle}
                      </h3>
                    </div>
                    <p className="text-gray-700 leading-relaxed text-center">
                      {aboutContent?.missionContent}
                    </p>
                  </CardContent>
                </Card>

                <Card className="border-2 border-orange-100">
                  <CardContent className="p-8">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Star className="w-8 h-8 text-orange-600" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900">
                        {aboutContent?.visionTitle}
                      </h3>
                    </div>
                    <p className="text-gray-700 leading-relaxed text-center">
                      {aboutContent?.visionContent}
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {aboutContent?.valuesTitle}
                </h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Nilai-nilai yang menjadi fondasi dalam setiap langkah perjalanan Acikoo
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {aboutContent?.values.map((value, index) => (
                  <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex justify-center mb-4">
                        {getValueIcon(value.icon)}
                      </div>
                      <h4 className="font-semibold text-lg text-gray-900 mb-3">
                        {value.title}
                      </h4>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {value.description}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Contact Info */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Kunjungi Kami
                </h2>
                <p className="text-gray-600">
                  Datang langsung ke toko atau hubungi kami untuk informasi lebih lanjut
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <Card>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <MapPin className="w-5 h-5 text-red-600 mt-1" />
                        <div>
                          <h4 className="font-semibold text-gray-900">Alamat</h4>
                          <p className="text-gray-600">{STORE_CONFIG.location.address}</p>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Clock className="w-5 h-5 text-red-600 mt-1" />
                        <div>
                          <h4 className="font-semibold text-gray-900">Jam Operasional</h4>
                          <p className="text-gray-600">Senin - Minggu: 17.00 - 23.00 WIB</p>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Phone className="w-5 h-5 text-red-600 mt-1" />
                        <div>
                          <h4 className="font-semibold text-gray-900">WhatsApp</h4>
                          <a href={`https://wa.me/${STORE_CONFIG.contact.whatsapp}`} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-green-600 transition-colors">
                            {STORE_CONFIG.contact.phone}
                          </a>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Mail className="w-5 h-5 text-red-600 mt-1" />
                        <div>
                          <h4 className="font-semibold text-gray-900">Email</h4>
                          <a href={`mailto:${STORE_CONFIG.contact.email}`} className="text-gray-600 hover:text-blue-600 transition-colors">
                            {STORE_CONFIG.contact.email}
                          </a>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Siap untuk Memesan?</h4>
                    <p className="text-gray-600 mb-6">
                      Nikmati aci pedas terenak dengan delivery cepat ke seluruh Jakarta Timur
                    </p>
                    <div className="space-y-3">
                      <Button className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700" asChild>
                        <a href="/products">
                          <ChefHat className="w-4 h-4 mr-2" />
                          Pesan Sekarang
                        </a>
                      </Button>
                      <Button variant="outline" className="w-full" asChild>
                        <a href={`https://wa.me/${STORE_CONFIG.contact.whatsapp}`} target="_blank" rel="noopener noreferrer">
                          <Phone className="w-4 h-4 mr-2" />
                          Chat WhatsApp
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
