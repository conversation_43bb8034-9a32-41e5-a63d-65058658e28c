'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useSidebar } from '@/contexts/sidebar-context'
import {
  LayoutDashboard,
  Package,
  MessageSquare,
  ChevronLeft,
  ChevronRight,
  Home,
  ShoppingCart,
  MapPin,
} from 'lucide-react'

const navigation = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
  },
  {
    name: 'Produk',
    href: '/admin/products',
    icon: Package,
    badge: 'lowStockProducts',
    children: [
      { name: '<PERSON><PERSON><PERSON>', href: '/admin/products' },
      { name: 'Tambah Produk', href: '/admin/products/new' },
      { name: '<PERSON><PERSON><PERSON>', href: '/admin/categories' },
    ],
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    href: '/admin/orders',
    icon: ShoppingCart,
    badge: 'pendingOrders',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    href: '/admin/delivery-analytics',
    icon: MapPin,
  },
  {
    name: 'Pesan',
    href: '/admin/messages',
    icon: MessageSquare,
    badge: 'unreadMessages',
  },
]

export function AdminSidebar() {
  const { collapsed, toggleCollapsed } = useSidebar()
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const [notifications, setNotifications] = useState<{ [key: string]: number }>({})
  const pathname = usePathname()

  useEffect(() => {
    fetchNotifications()

    // Update notifications every 30 seconds
    const interval = setInterval(fetchNotifications, 30000)
    return () => clearInterval(interval)
  }, [])

  const fetchNotifications = async () => {
    try {
      const response = await fetch('/api/admin/dashboard/stats?timeRange=24h')
      const result = await response.json()

      if (response.ok && result.success) {
        setNotifications({
          unreadMessages: result.data.unreadMessages || 0,
          lowStockProducts: result.data.lowStockProducts || 0,
          pendingOrders: result.data.totalOrders || 0,
        })
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
    }
  }

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  return (
    <>
      {/* Mobile backdrop */}
      <div className="lg:hidden fixed inset-0 bg-black/50 z-40" />

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 bg-white border-r border-gray-200 transition-all duration-300",
        collapsed ? "w-16" : "w-64"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          {!collapsed && (
            <Link href="/admin" className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-lg overflow-hidden">
                <Image
                  src="/img/logo.jpeg"
                  alt="Acikoo Logo"
                  width={40}
                  height={40}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-gray-900">Acikoo</span>
                <span className="text-xs text-gray-600">Admin Panel</span>
              </div>
            </Link>
          )}
          {collapsed && (
            <Link href="/admin" className="flex items-center justify-center">
              <div className="w-8 h-8 rounded-lg overflow-hidden">
                <Image
                  src="/img/logo.jpeg"
                  alt="Acikoo Logo"
                  width={32}
                  height={32}
                  className="w-full h-full object-cover"
                />
              </div>
            </Link>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleCollapsed}
            className="p-1.5"
          >
            {collapsed ? (
              <ChevronRight className="w-4 h-4" />
            ) : (
              <ChevronLeft className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {/* Back to Store */}
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start text-gray-600 hover:text-blue-600 hover:bg-blue-50",
              collapsed && "justify-center px-2"
            )}
            onClick={() => window.open('/', '_blank', 'noopener,noreferrer')}
          >
            <Home className="w-5 h-5" />
            {!collapsed && <span className="ml-3">Lihat Toko</span>}
          </Button>

          <div className="border-t border-gray-200 pt-4 mt-4">
            {navigation.map((item) => {
              // Fix active state logic to prevent Dashboard from always being active
              const isActive = item.href === '/admin'
                ? pathname === '/admin'
                : pathname === item.href || pathname.startsWith(item.href + '/')
              const isExpanded = expandedItems.includes(item.name)
              const hasChildren = item.children && item.children.length > 0

              return (
                <div key={item.name}>
                  <div className="relative">
                    {hasChildren ? (
                      <Button
                        variant="ghost"
                        onClick={() => toggleExpanded(item.name)}
                        className={cn(
                          "w-full justify-start text-gray-700 hover:text-blue-600 hover:bg-blue-50",
                          isActive && "bg-blue-50 text-blue-600",
                          collapsed && "justify-center px-2"
                        )}
                      >
                        <item.icon className="w-5 h-5" />
                        {!collapsed && (
                          <>
                            <span className="ml-3 flex-1 text-left">{item.name}</span>
                            {item.badge && notifications[item.badge] > 0 && (
                              <Badge variant="destructive" className="ml-auto">
                                {notifications[item.badge]}
                              </Badge>
                            )}
                          </>
                        )}
                      </Button>
                    ) : (
                      <Link href={item.href}>
                        <Button
                          variant="ghost"
                          className={cn(
                            "w-full justify-start text-gray-700 hover:text-blue-600 hover:bg-blue-50",
                            isActive && "bg-blue-50 text-blue-600",
                            collapsed && "justify-center px-2"
                          )}
                        >
                          <item.icon className="w-5 h-5" />
                          {!collapsed && (
                            <>
                              <span className="ml-3 flex-1 text-left">{item.name}</span>
                              {item.badge && notifications[item.badge] > 0 && (
                                <Badge variant="destructive" className="ml-auto">
                                  {notifications[item.badge]}
                                </Badge>
                              )}
                            </>
                          )}
                        </Button>
                      </Link>
                    )}
                  </div>

                  {/* Submenu */}
                  {hasChildren && isExpanded && !collapsed && (
                    <div className="ml-8 mt-2 space-y-1">
                      {item.children?.map((child) => (
                        <Link key={child.href} href={child.href}>
                          <Button
                            variant="ghost"
                            size="sm"
                            className={cn(
                              "w-full justify-start text-gray-600 hover:text-blue-600 hover:bg-blue-50",
                              pathname === child.href && "bg-blue-50 text-blue-600"
                            )}
                          >
                            {child.name}
                          </Button>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </nav>

        {/* Footer */}
        {!collapsed && (
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              <p>Acikoo Admin v1.0</p>
              <p>© 2024 Acikoo Store</p>
            </div>
          </div>
        )}
      </div>
    </>
  )
}
