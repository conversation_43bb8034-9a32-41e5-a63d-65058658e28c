import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { CartItem, Cart } from '@/types'

interface CartStore extends Cart {
  // Actions
  addItem: (item: Omit<CartItem, 'quantity'>) => void
  removeItem: (productId: string) => void
  updateQuantity: (productId: string, quantity: number) => void
  clearCart: (silent?: boolean) => void
  getTotalPrice: () => number
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      total: 0,
      itemCount: 0,
      isOpen: false,

      setIsOpen: (open: boolean) => set({ isOpen: open }),

      addItem: (newItem) => {
        const items = get().items
        const existingItem = items.find(item => item.productId === newItem.productId)

        if (existingItem) {
          // Update quantity if item already exists
          const updatedItems = items.map(item =>
            item.productId === newItem.productId
              ? { ...item, quantity: Math.min(item.quantity + 1, item.stock) }
              : item
          )

          const total = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
          const itemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)

          set({ items: updatedItems, total, itemCount })
        } else {
          // Add new item
          const cartItem: CartItem = { ...newItem, quantity: 1 }
          const updatedItems = [...items, cartItem]

          const total = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
          const itemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)

          set({ items: updatedItems, total, itemCount })
        }
      },

      removeItem: (productId) => {
        const items = get().items
        const updatedItems = items.filter(item => item.productId !== productId)

        const total = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
        const itemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)

        set({ items: updatedItems, total, itemCount })
      },

      updateQuantity: (productId, quantity) => {
        if (quantity <= 0) {
          get().removeItem(productId)
          return
        }

        const items = get().items
        const updatedItems = items.map(item =>
          item.productId === productId
            ? { ...item, quantity: Math.min(quantity, item.stock) }
            : item
        )

        const total = updatedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
        const itemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)

        set({ items: updatedItems, total, itemCount })
      },

      clearCart: (silent = false) => {
        set({ items: [], total: 0, itemCount: 0 })
        if (!silent) {
          // Only close cart modal if not silent
          set({ isOpen: false })
        }
      },

      getTotalPrice: () => {
        const items = get().items
        return items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
      },
    }),
    {
      name: 'acikoo-cart',
      partialize: (state) => ({
        items: state.items,
        total: state.total,
        itemCount: state.itemCount,
      }),
    }
  )
)
