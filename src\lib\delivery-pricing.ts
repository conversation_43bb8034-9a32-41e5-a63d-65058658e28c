import { STORE_CONFIG } from './store-config'

// Store location from centralized config
export const STORE_LOCATION = {
  lat: STORE_CONFIG.location.latitude,
  lng: STORE_CONFIG.location.longitude,
  address: STORE_CONFIG.location.address
} as const

// Delivery zones from store config
export const DELIVERY_ZONES = STORE_CONFIG.delivery.zones.map((zone, index) => ({
  id: index + 1,
  name: zone.name,
  description: zone.description,
  radius: zone.maxDistance * 1000, // convert km to meters for map circles
  price: zone.fee,
  color: zone.color,
  minDistance: index === 0 ? 0 : STORE_CONFIG.delivery.zones[index - 1].maxDistance,
  maxDistance: zone.maxDistance
}))

export const MAX_DELIVERY_DISTANCE = STORE_CONFIG.delivery.maxDeliveryDistance

// Calculate distance between two points using Haversine formula
export function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371 // Earth's radius in km
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

// Calculate distance from store to given coordinates
export function calculateDistanceFromStore(lat: number, lng: number): number {
  return calculateDistance(STORE_LOCATION.lat, STORE_LOCATION.lng, lat, lng)
}

// Determine delivery zone and pricing based on distance
export function getDeliveryZoneInfo(lat: number, lng: number) {
  const distance = calculateDistanceFromStore(lat, lng)

  // Check if within delivery area
  if (distance > MAX_DELIVERY_DISTANCE) {
    return {
      zone: 0,
      zoneName: 'Di luar area pengiriman',
      zoneDescription: `Jarak ${distance.toFixed(1)}km melebihi batas maksimal ${MAX_DELIVERY_DISTANCE}km`,
      deliveryFee: 0,
      distance,
      isDeliverable: false,
      zoneData: null
    }
  }

  // Find appropriate zone using store config logic
  const storeZone = STORE_CONFIG.delivery.zones.find(z => distance <= z.maxDistance)

  if (storeZone) {
    const zoneIndex = STORE_CONFIG.delivery.zones.indexOf(storeZone)
    return {
      zone: zoneIndex + 1,
      zoneName: storeZone.name,
      zoneDescription: storeZone.description,
      deliveryFee: storeZone.fee,
      distance,
      isDeliverable: true,
      zoneData: {
        ...storeZone,
        id: zoneIndex + 1,
        radius: storeZone.maxDistance * 1000,
        price: storeZone.fee,
        minDistance: zoneIndex === 0 ? 0 : STORE_CONFIG.delivery.zones[zoneIndex - 1].maxDistance,
        maxDistance: storeZone.maxDistance
      }
    }
  }

  // Fallback (shouldn't reach here if zones are configured correctly)
  return {
    zone: 0,
    zoneName: 'Di luar area pengiriman',
    zoneDescription: 'Lokasi tidak dapat dilayani',
    deliveryFee: 0,
    distance,
    isDeliverable: false,
    zoneData: null
  }
}

// Validate delivery location
export function validateDeliveryLocation(lat: number, lng: number): {
  isValid: boolean
  message: string
  zoneInfo: ReturnType<typeof getDeliveryZoneInfo>
} {
  const zoneInfo = getDeliveryZoneInfo(lat, lng)

  if (!zoneInfo.isDeliverable) {
    return {
      isValid: false,
      message: `Maaf, lokasi Anda berada di luar area pengiriman kami. ${zoneInfo.zoneDescription}`,
      zoneInfo
    }
  }

  return {
    isValid: true,
    message: `Lokasi Anda berada di ${zoneInfo.zoneName} dengan ongkir ${formatCurrency(zoneInfo.deliveryFee)}`,
    zoneInfo
  }
}

// Format currency helper
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Get zone color for UI
export function getZoneColor(zoneId: number): string {
  const zone = DELIVERY_ZONES.find(z => z.id === zoneId)
  return zone?.color || '#6b7280'
}

// Calculate total order amount
export function calculateOrderTotal(subtotal: number, deliveryFee: number): number {
  return subtotal + deliveryFee
}

// Pricing summary for display
export interface PricingSummary {
  subtotal: number
  deliveryFee: number
  total: number
  zoneName?: string
  zoneDescription?: string
  isDeliverable: boolean
}

export function createPricingSummary(
  subtotal: number,
  lat?: number,
  lng?: number
): PricingSummary {
  if (!lat || !lng) {
    return {
      subtotal,
      deliveryFee: 0,
      total: subtotal,
      isDeliverable: false
    }
  }

  const zoneInfo = getDeliveryZoneInfo(lat, lng)

  return {
    subtotal,
    deliveryFee: zoneInfo.deliveryFee,
    total: calculateOrderTotal(subtotal, zoneInfo.deliveryFee),
    zoneName: zoneInfo.zoneName,
    zoneDescription: zoneInfo.zoneDescription,
    isDeliverable: zoneInfo.isDeliverable
  }
}
