'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft,
  Edit, 
  Trash2,
  Package,
  Eye,
  EyeOff,
  Calendar,
  Tag,
  Weight,
  DollarSign,
  BarChart3
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { toast } from 'sonner'

interface Product {
  id: string
  name: string
  description?: string
  price: number
  images?: string[]
  stock: number
  isActive: boolean
  weight?: number
  categoryId: string
  createdAt: string
  updatedAt: string
  category: {
    id: string
    name: string
  }
  orderItems?: {
    id: string
    quantity: number
    order: {
      id: string
      orderNumber: string
      createdAt: string
      status: string
    }
  }[]
}

export default function ProductDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchProduct()
    }
  }, [params.id])

  const fetchProduct = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/products/${params.id}`)
      const result = await response.json()

      if (response.ok && result.success) {
        setProduct(result.data)
      } else {
        toast.error(result.error || 'Gagal memuat detail produk')
        router.push('/admin/products')
      }
    } catch (error) {
      console.error('Error fetching product:', error)
      toast.error('Terjadi kesalahan saat memuat detail produk')
      router.push('/admin/products')
    } finally {
      setLoading(false)
    }
  }

  const toggleProductStatus = async () => {
    if (!product) return

    try {
      const response = await fetch(`/api/admin/products/${product.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !product.isActive })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast.success(`Produk berhasil ${!product.isActive ? 'diaktifkan' : 'dinonaktifkan'}`)
        setProduct(prev => prev ? { ...prev, isActive: !prev.isActive } : null)
      } else {
        toast.error(result.error || 'Gagal mengubah status produk')
      }
    } catch (error) {
      console.error('Error updating product:', error)
      toast.error('Terjadi kesalahan saat mengubah status')
    }
  }

  const deleteProduct = async () => {
    if (!product) return
    if (!confirm('Apakah Anda yakin ingin menghapus produk ini?')) return

    try {
      const response = await fetch(`/api/admin/products/${product.id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast.success('Produk berhasil dihapus')
        router.push('/admin/products')
      } else {
        toast.error(result.error || 'Gagal menghapus produk')
      }
    } catch (error) {
      console.error('Error deleting product:', error)
      toast.error('Terjadi kesalahan saat menghapus produk')
    }
  }

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="destructive">Habis</Badge>
    } else if (stock <= 10) {
      return <Badge variant="secondary" className="bg-orange-100 text-orange-700">Menipis</Badge>
    } else {
      return <Badge variant="default" className="bg-green-100 text-green-700">Tersedia</Badge>
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-96 bg-gray-200 rounded"></div>
            <div className="space-y-4">
              <div className="h-6 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="text-center py-12">
        <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Produk Tidak Ditemukan
        </h3>
        <p className="text-gray-600 mb-4">
          Produk yang Anda cari tidak ditemukan atau telah dihapus.
        </p>
        <Button asChild>
          <Link href="/admin/products">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali ke Daftar Produk
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Kembali
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
            <p className="text-gray-600">Detail informasi produk</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={toggleProductStatus}>
            {product.isActive ? (
              <>
                <EyeOff className="w-4 h-4 mr-2" />
                Nonaktifkan
              </>
            ) : (
              <>
                <Eye className="w-4 h-4 mr-2" />
                Aktifkan
              </>
            )}
          </Button>
          
          <Button variant="outline" asChild>
            <Link href={`/admin/products/${product.id}/edit`}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Link>
          </Button>
          
          <Button variant="outline" onClick={deleteProduct} className="text-red-600 hover:text-red-700">
            <Trash2 className="w-4 h-4 mr-2" />
            Hapus
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Product Images */}
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              {product.images && product.images.length > 0 ? (
                <>
                  <div className="relative h-96 bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={product.images[0]}
                      alt={product.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  
                  {product.images.length > 1 && (
                    <div className="grid grid-cols-4 gap-2">
                      {product.images.slice(1, 5).map((image, index) => (
                        <div key={index} className="relative h-20 bg-gray-100 rounded overflow-hidden">
                          <Image
                            src={image}
                            alt={`${product.name} ${index + 2}`}
                            fill
                            className="object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
                  <Package className="w-16 h-16 text-gray-400" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Product Info */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Informasi Produk
                <Badge variant={product.isActive ? 'default' : 'secondary'}>
                  {product.isActive ? 'Aktif' : 'Nonaktif'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Harga</p>
                    <p className="font-semibold text-lg text-red-600">
                      {formatCurrency(product.price)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Package className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Stok</p>
                    <div className="flex items-center space-x-2">
                      <p className="font-semibold">{product.stock} unit</p>
                      {getStockBadge(product.stock)}
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Tag className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Kategori</p>
                    <p className="font-medium">{product.category.name}</p>
                  </div>
                </div>

                {product.weight && (
                  <div className="flex items-center space-x-2">
                    <Weight className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Berat</p>
                      <p className="font-medium">{product.weight}g</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Dibuat</p>
                    <p className="font-medium">
                      {new Date(product.createdAt).toLocaleDateString('id-ID', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                      })}
                    </p>
                  </div>
                </div>
              </div>

              {product.description && (
                <>
                  <Separator />
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Deskripsi</p>
                    <p className="text-gray-800">{product.description}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Sales Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Analisis Penjualan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-blue-600">
                    {product.orderItems?.length || 0}
                  </p>
                  <p className="text-sm text-gray-600">Total Pesanan</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">
                    {product.orderItems?.reduce((sum, item) => sum + item.quantity, 0) || 0}
                  </p>
                  <p className="text-sm text-gray-600">Unit Terjual</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Recent Orders */}
      {product.orderItems && product.orderItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Pesanan Terbaru</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {product.orderItems.slice(0, 5).map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">#{item.order.orderNumber}</p>
                    <p className="text-sm text-gray-600">
                      {new Date(item.order.createdAt).toLocaleDateString('id-ID')}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{item.quantity} unit</p>
                    <Badge variant="secondary">{item.order.status}</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
