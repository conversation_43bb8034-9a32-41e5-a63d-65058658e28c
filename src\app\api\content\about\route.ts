import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Get about content from database
    const aboutContent = await prisma.contentSection.findFirst({
      where: { 
        key: 'about',
        isActive: true 
      }
    })

    if (!aboutContent) {
      // Return default about content
      const defaultContent = {
        heroTitle: "Tentang Acikoo",
        heroSubtitle: "Aci Pedas Terenak di Jakarta Timur",
        heroDescription: "Sejak 2020, Acikoo telah menjadi destinasi favorit untuk jajanan aci pedas yang autentik dan menggugah selera. Dengan resep rahasia turun temurun dan bahan-bahan pilihan, kami menghadirkan cita rasa yang tak terlupakan.",
        storyTitle: "Cerita Kami",
        storyContent: "Acikoo lahir dari kecintaan terhadap jajanan tradisional Indonesia, khususnya aci pedas. Di<PERSON>lai dari warung kecil di Cipayung, Jakarta Timur, kami berkomitmen untuk menghadirkan aci pedas dengan kualitas terbaik dan cita rasa yang konsisten. Setiap porsi dibuat dengan penuh cinta dan perhatian detail.",
        missionTitle: "Misi Kami",
        missionContent: "Menghadirkan jajanan aci pedas berkualitas tinggi dengan pelayanan terbaik, sambil melestarikan cita rasa tradisional Indonesia untuk generasi mendatang.",
        visionTitle: "Visi Kami",
        visionContent: "Menjadi brand aci pedas terdepan di Indonesia yang dikenal karena kualitas, inovasi, dan pelayanan yang memuaskan pelanggan.",
        valuesTitle: "Nilai-Nilai Kami",
        values: [
          {
            icon: "quality",
            title: "Kualitas Terjamin",
            description: "Menggunakan bahan-bahan pilihan dan proses pembuatan yang higienis"
          },
          {
            icon: "service",
            title: "Pelayanan Prima",
            description: "Memberikan pengalaman berbelanja yang menyenangkan dan memuaskan"
          },
          {
            icon: "innovation",
            title: "Inovasi Berkelanjutan",
            description: "Terus berinovasi dalam rasa dan pelayanan untuk kepuasan pelanggan"
          },
          {
            icon: "trust",
            title: "Kepercayaan",
            description: "Membangun hubungan jangka panjang berdasarkan kepercayaan dan kualitas"
          }
        ]
      }

      return NextResponse.json({
        success: true,
        data: defaultContent
      })
    }

    let parsedContent
    try {
      parsedContent = JSON.parse(aboutContent.content)
    } catch (error) {
      console.error('Error parsing about content:', error)
      return NextResponse.json(
        { success: false, error: 'Konten about tidak valid' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: parsedContent
    })
  } catch (error) {
    console.error('Error fetching about content:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal memuat konten about' },
      { status: 500 }
    )
  }
}
