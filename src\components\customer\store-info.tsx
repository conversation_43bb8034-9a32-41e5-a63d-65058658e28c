'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { StoreLocationMap } from '@/components/maps/store-location-map'
import {
  Clock,
  MapPin,
  Phone,
  Mail,
  Truck,
  CreditCard,
  Shield,
  Star,
  ExternalLink
} from 'lucide-react'
import Link from 'next/link'
import {
  STORE_CONFIG,
  getStoreLocation,
  getContactInfo,
  getDeliveryZones,
  isStoreOpen,
  formatOperatingHours,
  getOperatingHours
} from '@/lib/store-config'

export function StoreInfo() {
  const storeLocation = getStoreLocation()
  const contactInfo = getContactInfo()
  const operatingHours = getOperatingHours()
  const deliveryZones = getDeliveryZones()
  const isOpen = isStoreOpen()

  const storeHours = [
    { day: 'Senin - Jumat', hours: '17:00 - 23:00', isOpen: true },
    { day: 'Sabtu', hours: '17:00 - 23:00', isOpen: true },
    { day: 'Minggu', hours: '17:00 - 23:00', isOpen: true },
  ]

  const features = [
    {
      icon: Truck,
      title: 'Pengiriman Cepat',
      description: 'Pengiriman dalam 1-2 jam dalam radius 9km',
      color: 'text-blue-600'
    },
    {
      icon: CreditCard,
      title: 'Pembayaran QRIS',
      description: 'Pembayaran mudah dan aman dengan QRIS',
      color: 'text-green-600'
    },
    {
      icon: Shield,
      title: 'Produk Berkualitas',
      description: 'Semua produk telah melalui quality control',
      color: 'text-purple-600'
    },
    {
      icon: Star,
      title: 'Pelayanan Terbaik',
      description: 'Customer service 24/7 siap membantu',
      color: 'text-yellow-600'
    }
  ]

  return (
    <section className="space-y-8">
      {/* Section Header */}
      <div className="text-center space-y-4">
        <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
          Informasi Toko
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Kenali lebih dekat toko kami dan layanan yang tersedia
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Store Details */}
        <div className="space-y-6">
          {/* Contact Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="w-5 h-5 text-blue-600" />
                <span>Kontak & Lokasi</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-gray-400 mt-0.5" />
                <div>
                  <p className="font-medium">Alamat Toko</p>
                  <p className="text-gray-600">
                    {storeLocation.address}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">{storeLocation.landmark}</p>
                  <Button variant="outline" size="sm" className="mt-2" asChild>
                    <Link href="/store-location">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Lihat Detail Lokasi
                    </Link>
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium">WhatsApp</p>
                  <p className="text-gray-600">{contactInfo.whatsapp}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-gray-600">{contactInfo.email}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Store Hours */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-green-600" />
                <span>Jam Operasional</span>
                <Badge variant={isOpen ? 'default' : 'destructive'} className={isOpen ? 'bg-green-100 text-green-700' : ''}>
                  {isOpen ? 'Buka' : 'Tutup'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {storeHours.map((schedule, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="font-medium">{schedule.day}</span>
                  <span className="text-gray-600">{schedule.hours}</span>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Map Placeholder & Features */}
        <div className="space-y-6">
          {/* Interactive Delivery Zone Map */}
          <Card className="relative z-10">
            <CardHeader>
              <CardTitle>Area Pengiriman</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative z-10">
                <StoreLocationMap height="h-80" showDeliveryZones={true} />
              </div>
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Area Pengiriman:</strong> Kami melayani pengiriman dalam radius 9km dari lokasi toko.
                  Biaya pengiriman mulai dari Rp 10.000 hingga Rp 25.000 tergantung jarak.
                </p>
              </div>
              <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span>Zone 1-2: Rp 10k-13k</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <span>Zone 3-4: Rp 16k-19k</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <span>Zone 5-6: Rp 22k-25k</span>
                </div>
                <div className="text-center">
                  <Link href="/store-location" className="text-blue-600 hover:text-blue-800">
                    Lihat Detail →
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {features.map((feature, index) => (
          <Card key={index} className="text-center hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-6">
              <div className={`w-12 h-12 mx-auto mb-4 rounded-lg bg-gray-100 flex items-center justify-center ${feature.color}`}>
                <feature.icon className="w-6 h-6" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
              <p className="text-sm text-gray-600">{feature.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Delivery Info */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="p-8">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-blue-600 rounded-full flex items-center justify-center">
              <Truck className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900">
              Pengiriman Gratis*
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Nikmati pengiriman gratis untuk pembelian minimal Rp 50.000 dalam radius 3km.
              Untuk area lainnya, biaya pengiriman mulai dari Rp 10.000.
            </p>
            <p className="text-sm text-gray-500">
              *Syarat dan ketentuan berlaku
            </p>
          </div>
        </CardContent>
      </Card>
    </section>
  )
}
