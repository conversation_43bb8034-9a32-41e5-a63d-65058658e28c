import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateProductSchema = z.object({
  name: z.string().min(1, 'Nama produk wajib diisi').optional(),
  description: z.string().optional(),
  price: z.union([z.number(), z.string()]).transform((val) => {
    const num = typeof val === 'string' ? parseFloat(val) : val
    if (isNaN(num) || num <= 0) {
      throw new Error('Harga harus lebih dari 0')
    }
    return num
  }).optional(),
  categoryId: z.string().min(1, 'Kategori wajib dipilih').optional(),
  stock: z.union([z.number(), z.string()]).transform((val) => {
    const num = typeof val === 'string' ? parseInt(val) : val
    if (isNaN(num) || num < 0) {
      throw new Error('Stok tidak boleh negatif')
    }
    return num
  }).optional(),

  images: z.array(z.string()).optional(),
  isActive: z.boolean().optional()
})

// GET /api/admin/products/[id] - Get product detail for admin
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: productId } = await params

    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        },
        orderItems: {
          include: {
            order: {
              select: {
                id: true,
                orderNumber: true,
                createdAt: true,
                status: true,
                paymentMethod: true,
                total: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true
                  }
                },
                payment: {
                  select: {
                    id: true,
                    status: true,
                    method: true,
                    amount: true,
                    verifiedAt: true
                  }
                }
              }
            }
          },
          orderBy: {
            order: {
              createdAt: 'desc'
            }
          },
          take: 10 // Limit to recent 10 orders
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Produk tidak ditemukan' },
        { status: 404 }
      )
    }

    // Parse images if stored as JSON string and format price
    const formattedProduct = {
      ...product,
      price: Number(product.price),
      images: product.images ? JSON.parse(product.images) : [],
      // Format order items with proper payment status
      orderItems: product.orderItems.map(item => ({
        ...item,
        price: Number(item.price),
        subtotal: Number(item.subtotal),
        order: {
          ...item.order,
          total: Number(item.order.total),
          paymentStatus: item.order.payment?.status || 'PENDING' // Get payment status from payment relation
        }
      }))
    }

    return NextResponse.json({
      success: true,
      data: formattedProduct
    })

  } catch (error) {
    console.error('Get product detail error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/admin/products/[id] - Update product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: productId } = await params

    if (!productId) {
      return NextResponse.json(
        { success: false, error: 'Product ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    console.log('Update product request body:', body)

    const data = updateProductSchema.parse(body)
    console.log('Parsed data:', data)

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        category: true
      }
    })

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: 'Produk tidak ditemukan' },
        { status: 404 }
      )
    }

    // Check if category exists (if categoryId is being updated)
    if (data.categoryId) {
      const category = await prisma.category.findUnique({
        where: { id: data.categoryId }
      })

      if (!category) {
        return NextResponse.json(
          { success: false, error: 'Kategori tidak ditemukan' },
          { status: 400 }
        )
      }
    }

    // Check if product name already exists (if name is being updated)
    if (data.name && data.name !== existingProduct.name) {
      const duplicateProduct = await prisma.product.findFirst({
        where: {
          name: data.name, // MySQL is case-insensitive by default for VARCHAR
          id: {
            not: productId
          }
        }
      })

      if (duplicateProduct) {
        return NextResponse.json(
          { success: false, error: 'Produk dengan nama ini sudah ada' },
          { status: 400 }
        )
      }
    }

    // Prepare update data
    const updateData: any = {}

    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.price !== undefined) updateData.price = data.price
    if (data.categoryId !== undefined) updateData.categoryId = data.categoryId
    if (data.stock !== undefined) updateData.stock = data.stock
    if (data.images !== undefined) updateData.images = JSON.stringify(data.images)
    if (data.isActive !== undefined) updateData.isActive = data.isActive

    console.log('Update data:', updateData)

    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: updateData,
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Format response
    const formattedProduct = {
      ...updatedProduct,
      price: Number(updatedProduct.price),
      images: updatedProduct.images ? JSON.parse(updatedProduct.images) : []
    }

    return NextResponse.json({
      success: true,
      message: 'Produk berhasil diperbarui',
      data: formattedProduct
    })

  } catch (error) {
    console.error('Update product error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0]?.message || 'Data tidak valid', details: error.errors },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/products/[id] - Delete product
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('DELETE product request received')

    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      console.log('Unauthorized delete attempt')
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: productId } = await params
    console.log('Deleting product with ID:', productId)

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        orderItems: true
      }
    })

    if (!existingProduct) {
      console.log('Product not found:', productId)
      return NextResponse.json(
        { success: false, error: 'Produk tidak ditemukan' },
        { status: 404 }
      )
    }

    console.log('Product found:', existingProduct.name)
    console.log('Order items count:', existingProduct.orderItems.length)

    // Check if product has order items
    if (existingProduct.orderItems.length > 0) {
      console.log('Cannot delete product with order items')
      return NextResponse.json(
        {
          success: false,
          error: 'Tidak dapat menghapus produk yang sudah pernah dipesan. Nonaktifkan produk sebagai gantinya.'
        },
        { status: 400 }
      )
    }

    console.log('Deleting product from database...')
    await prisma.product.delete({
      where: { id: productId }
    })

    console.log('Product deleted successfully')
    return NextResponse.json({
      success: true,
      message: 'Produk berhasil dihapus'
    })

  } catch (error) {
    console.error('Delete product error:', error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
