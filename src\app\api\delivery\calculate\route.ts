import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import {
  getStoreLocation,
  getDeliveryZones,
  calculateDeliveryInfo,
  getFreeDeliveryThreshold
} from '@/lib/store-config'
import { geocodeAddress } from '@/lib/leaflet-maps'

const calculateDeliverySchema = z.object({
  address: z.string().min(1, 'Al<PERSON>t wajib diisi'),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  subtotal: z.number().positive('Subtotal harus lebih dari 0'),
})

// Calculate distance between two coordinates using Haversine formula
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371 // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLon = (lon2 - lon1) * Math.PI / 180
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

// Geocode address using Nominatim (OpenStreetMap)
async function geocodeAddressToCoordinates(address: string): Promise<{ latitude: number, longitude: number } | null> {
  try {
    const coordinates = await geocodeAddress(address)
    if (coordinates) {
      return { latitude: coordinates.lat, longitude: coordinates.lng }
    }
    return null
  } catch (error) {
    console.error('Geocoding error:', error)
    return null
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const data = calculateDeliverySchema.parse(body)

    let customerLat = data.latitude
    let customerLon = data.longitude

    // If coordinates not provided, geocode from address
    if (!customerLat || !customerLon) {
      const geocoded = await geocodeAddressToCoordinates(data.address)
      if (geocoded) {
        customerLat = geocoded.latitude
        customerLon = geocoded.longitude
      } else {
        // Fallback to store location if geocoding fails
        const storeLocation = getStoreLocation()
        customerLat = storeLocation.latitude
        customerLon = storeLocation.longitude
      }
    }

    // Calculate delivery info using centralized config
    const deliveryInfo = calculateDeliveryInfo(customerLat, customerLon, data.subtotal)
    const storeLocation = getStoreLocation()

    if (!deliveryInfo.isDeliverable) {
      return NextResponse.json({
        success: false,
        error: 'Untuk saat ini layanan pengiriman di area Anda belum tersedia',
        message: 'Kami sedang berusaha memperluas jangkauan untuk melayani Anda lebih baik!',
        data: {
          distance: deliveryInfo.distance,
          maxDeliveryDistance: getDeliveryZones()[getDeliveryZones().length - 1].maxDistance,
          isDeliverable: false,
          alternatives: {
            pickup: {
              available: true,
              address: 'Jl. Tanah Merah No.15, RT.15/RW.8, Pluit, Penjaringan, Jakarta Utara 14440',
              contact: 'https://wa.me/6281295868699'
            }
          }
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        distance: deliveryInfo.distance,
        zone: deliveryInfo.zone,
        originalFee: deliveryInfo.originalFee,
        finalFee: deliveryInfo.finalFee,
        isFreeDelivery: deliveryInfo.isFreeDelivery,
        freeDeliveryThreshold: deliveryInfo.freeDeliveryThreshold,
        isDeliverable: deliveryInfo.isDeliverable,
        estimatedDeliveryTime: deliveryInfo.estimatedDeliveryTime,
        estimatedDeliveryMinutes: deliveryInfo.estimatedMinutes,
        coordinates: {
          customer: { latitude: customerLat, longitude: customerLon },
          store: storeLocation
        }
      }
    })

  } catch (error) {
    console.error('Calculate delivery error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Data tidak valid', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/delivery/calculate - Get delivery zones info
export async function GET() {
  try {
    const storeLocation = getStoreLocation()
    const deliveryZones = getDeliveryZones()
    const freeDeliveryThreshold = getFreeDeliveryThreshold()

    return NextResponse.json({
      success: true,
      data: {
        storeLocation,
        deliveryZones,
        freeDeliveryThreshold,
        maxDeliveryDistance: deliveryZones[deliveryZones.length - 1].maxDistance
      }
    })
  } catch (error) {
    console.error('Get delivery info error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
