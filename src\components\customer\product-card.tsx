'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Star, Eye, Heart, ShoppingCart } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { getProductImage as getProductImageUtil } from '@/lib/product-images'
import { ProductWithCategory } from '@/types'
import { toast } from 'sonner'
import Image from 'next/image'
import { useCartStore } from '@/store/cart'

interface ProductCardProps {
  product: ProductWithCategory
  viewMode?: 'grid' | 'list'
}

export function ProductCard({ product, viewMode = 'grid' }: ProductCardProps) {
  const [isFavorite, setIsFavorite] = useState(false)
  const { addItem } = useCartStore()

  // Use centralized image utility
  const getProductImage = (productName: string) => {
    return getProductImageUtil(productName)
  }

  const handleAddToCart = () => {
    if (product.stock === 0) {
      toast.error('Produk sedang habis')
      return
    }

    addItem({
      id: product.id,
      productId: product.id,
      name: product.name,
      price: Number(product.price),
      image: getProductImage(product.name),
      stock: product.stock,
    })

    toast.success('Ditambahkan ke keranjang!', {
      description: `${product.name} berhasil ditambahkan`
    })
  }



  const toggleFavorite = () => {
    setIsFavorite(!isFavorite)
    toast.success(isFavorite ? 'Dihapus dari favorit' : 'Ditambahkan ke favorit')
  }

  if (viewMode === 'list') {
    return (
      <Card className="hover:shadow-lg transition-shadow duration-300">
        <div className="flex">
          {/* Product Image */}
          <div className="w-48 h-36 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0">
            <Image
              src={getProductImage(product.name)}
              alt={product.name}
              width={192}
              height={144}
              className="w-full h-full object-cover"
            />
          </div>

          {/* Product Info */}
          <div className="flex-1 p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="secondary" className="text-xs">
                    {product.category.name}
                  </Badge>
                  {product.stock < 10 && product.stock > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      Stok Terbatas
                    </Badge>
                  )}
                  {product.stock === 0 && (
                    <Badge variant="outline" className="text-xs">
                      Stok Habis
                    </Badge>
                  )}
                </div>

                <Link href={`/products/${product.id}`}>
                  <h3 className="font-semibold text-lg text-gray-900 hover:text-blue-600 transition-colors mb-2">
                    {product.name}
                  </h3>
                </Link>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {product.description}
                </p>

                <div className="flex items-center gap-4 mb-4">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm text-gray-600">4.5</span>
                    <span className="text-sm text-gray-400">(24 ulasan)</span>
                  </div>
                  <span className="text-sm text-gray-500">
                    Stok: {product.stock}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-blue-600">
                    {formatCurrency(Number(product.price))}
                  </span>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={toggleFavorite}
                    >
                      <Heart className={`w-4 h-4 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
                    </Button>

                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/products/${product.id}`}>
                        <Eye className="w-4 h-4 mr-2" />
                        Detail
                      </Link>
                    </Button>

                    <Button
                      onClick={handleAddToCart}
                      disabled={product.stock === 0}
                      className="min-w-[120px] bg-red-600 hover:bg-red-700"
                    >
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      {product.stock === 0 ? 'Stok Habis' : 'Tambah'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card className="group hover:shadow-lg transition-shadow duration-300">
      <CardContent className="p-4">
        {/* Product Image */}
        <div className="relative aspect-[4/3] mb-4 bg-gray-100 rounded-lg overflow-hidden">
          <Image
            src={getProductImage(product.name)}
            alt={product.name}
            width={300}
            height={225}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.stock < 10 && product.stock > 0 && (
              <Badge variant="destructive" className="text-xs">
                Stok Terbatas
              </Badge>
            )}
            {product.stock === 0 && (
              <Badge variant="outline" className="text-xs bg-white">
                Stok Habis
              </Badge>
            )}
          </div>

          {/* Action Buttons */}
          <div className="absolute top-2 right-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFavorite}
              className="w-8 h-8 p-0 bg-white"
            >
              <Heart className={`w-4 h-4 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
            </Button>

            <Button
              variant="outline"
              size="sm"
              asChild
              className="w-8 h-8 p-0 bg-white"
            >
              <Link href={`/products/${product.id}`}>
                <Eye className="w-4 h-4" />
              </Link>
            </Button>
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Badge variant="secondary" className="text-xs">
              {product.category.name}
            </Badge>
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm text-gray-600">4.5</span>
            </div>
          </div>

          <Link href={`/products/${product.id}`}>
            <h3 className="font-semibold text-gray-900 line-clamp-2 hover:text-blue-600 transition-colors">
              {product.name}
            </h3>
          </Link>

          <p className="text-sm text-gray-600 line-clamp-2">
            {product.description}
          </p>

          <div className="flex items-center justify-between">
            <span className="text-lg font-bold text-blue-600">
              {formatCurrency(Number(product.price))}
            </span>
            <span className="text-sm text-gray-500">
              Stok: {product.stock}
            </span>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <Button
          className="w-full bg-red-600 hover:bg-red-700"
          onClick={handleAddToCart}
          disabled={product.stock === 0}
        >
          <ShoppingCart className="w-4 h-4 mr-2" />
          {product.stock === 0 ? 'Stok Habis' : 'Tambah ke Keranjang'}
        </Button>
      </CardFooter>
    </Card>
  )
}
