import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const homepageContentSchema = z.object({
  heroBanners: z.array(z.object({
    id: z.string(),
    title: z.string(),
    subtitle: z.string(),
    description: z.string(),
    image: z.string(),
    buttonText: z.string(),
    buttonLink: z.string(),
    isActive: z.boolean(),
    order: z.number()
  })),
  promotions: z.array(z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    image: z.string(),
    discount: z.string(),
    validUntil: z.string(),
    isActive: z.boolean(),
    order: z.number()
  })),
  featuredProducts: z.array(z.string()),
  aboutSection: z.object({
    title: z.string(),
    description: z.string(),
    image: z.string()
  }),
  testimonials: z.array(z.object({
    id: z.string(),
    name: z.string(),
    message: z.string(),
    rating: z.number(),
    isActive: z.boolean()
  }))
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get homepage content from database
    const homepageContent = await prisma.contentSection.findFirst({
      where: { key: 'homepage' }
    })

    if (!homepageContent) {
      // Return default structure
      const defaultContent = {
        heroBanners: [],
        promotions: [],
        featuredProducts: [],
        aboutSection: {
          title: 'Tentang Acikoo',
          description: 'Aci pedas terenak di Jakarta Utara dengan cita rasa autentik yang bikin nagih.',
          image: ''
        },
        testimonials: []
      }

      return NextResponse.json({
        success: true,
        data: defaultContent
      })
    }

    return NextResponse.json({
      success: true,
      data: JSON.parse(homepageContent.content)
    })
  } catch (error) {
    console.error('Error fetching homepage content:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal memuat konten homepage' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = homepageContentSchema.parse(body)

    // Upsert homepage content
    const homepageContent = await prisma.contentSection.upsert({
      where: { key: 'homepage' },
      update: {
        content: JSON.stringify(validatedData),
        updatedAt: new Date()
      },
      create: {
        key: 'homepage',
        title: 'Homepage Content',
        content: JSON.stringify(validatedData),
        type: 'JSON',
        isActive: true
      }
    })

    return NextResponse.json({
      success: true,
      data: JSON.parse(homepageContent.content),
      message: 'Konten homepage berhasil disimpan'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error('Error updating homepage content:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal menyimpan konten homepage' },
      { status: 500 }
    )
  }
}
