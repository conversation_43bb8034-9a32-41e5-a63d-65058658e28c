'use client'

import { useState, useEffect } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Bell,
  User,
  LogOut,
  Menu,
  MessageSquare,
  ShoppingCart,
} from 'lucide-react'

interface HeaderStats {
  pendingOrders: number
  unreadMessages: number
  lowStockProducts: number
}

interface Notification {
  id: string
  type: 'order' | 'message' | 'stock' | 'delivery'
  title: string
  message: string
  time: string
  unread: boolean
  createdAt: string
}

export function AdminHeader() {
  const { data: session } = useSession()
  const [searchQuery, setSearchQuery] = useState('')
  const [stats, setStats] = useState<HeaderStats>({
    pendingOrders: 0,
    unreadMessages: 0,
    lowStockProducts: 0
  })
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchHeaderData()

    // Update data every 30 seconds
    const interval = setInterval(fetchHeaderData, 30000)
    return () => clearInterval(interval)
  }, [])

  const fetchHeaderData = async () => {
    try {
      const [statsResponse, notificationsResponse] = await Promise.all([
        fetch('/api/admin/dashboard/stats?timeRange=24h'),
        fetch('/api/admin/notifications?limit=10')
      ])

      if (statsResponse.ok) {
        const statsResult = await statsResponse.json()
        if (statsResult.success) {
          setStats({
            pendingOrders: statsResult.data.pendingOrders || 0,
            unreadMessages: statsResult.data.unreadMessages || 0,
            lowStockProducts: statsResult.data.lowStockProducts || 0
          })
        }
      }

      if (notificationsResponse.ok) {
        const notificationsResult = await notificationsResponse.json()
        if (notificationsResult.success) {
          setNotifications(notificationsResult.data || [])
        }
      }
    } catch (error) {
      console.error('Error fetching header data:', error)
    } finally {
      setLoading(false)
    }
  }

  const unreadCount = notifications.filter(n => n.unread).length

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/signin' })
  }

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Search */}
        <div className="flex items-center space-x-4 flex-1">
          <Button variant="ghost" size="sm" className="lg:hidden">
            <Menu className="w-5 h-5" />
          </Button>

          <div className="relative max-w-md w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Cari pesanan, produk, pelanggan..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4"
            />
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-4">
          {/* Quick Stats */}
          <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <ShoppingCart className="w-4 h-4" />
              <span>{loading ? '...' : `${stats.pendingOrders} pesanan baru`}</span>
            </div>
            <div className="flex items-center space-x-1">
              <MessageSquare className="w-4 h-4" />
              <span>{loading ? '...' : `${stats.unreadMessages} pesan`}</span>
            </div>
          </div>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="w-5 h-5" />
                {unreadCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 w-5 h-5 text-xs flex items-center justify-center p-0"
                  >
                    {unreadCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <div className="p-3 border-b">
                <h3 className="font-semibold">Notifikasi</h3>
                <p className="text-sm text-gray-600">{unreadCount} notifikasi belum dibaca</p>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {loading ? (
                  <div className="p-4 text-center text-gray-500">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  </div>
                ) : notifications.length > 0 ? (
                  notifications.map((notification) => (
                    <DropdownMenuItem key={notification.id} className="p-3 cursor-pointer">
                      <div className="flex items-start space-x-3 w-full">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          notification.unread ? 'bg-blue-600' : 'bg-gray-300'
                        }`} />
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm">{notification.title}</p>
                          <p className="text-sm text-gray-600 truncate">{notification.message}</p>
                          <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    <p className="text-sm">Tidak ada notifikasi</p>
                  </div>
                )}
              </div>
              <div className="p-3 border-t">
                <Button variant="ghost" size="sm" className="w-full">
                  Lihat Semua Notifikasi
                </Button>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <div className="hidden md:block text-left">
                  <p className="text-sm font-medium">{session?.user?.name}</p>
                  <p className="text-xs text-gray-600">Administrator</p>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="p-3 border-b">
                <p className="font-medium">{session?.user?.name}</p>
                <p className="text-sm text-gray-600">{session?.user?.email}</p>
              </div>
              <DropdownMenuItem>
                <User className="w-4 h-4 mr-2" />
                Profil Saya
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
                <LogOut className="w-4 h-4 mr-2" />
                Keluar
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
