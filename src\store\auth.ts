import { create } from 'zustand'
import { User } from '@/types'

interface AuthStore {
  user: User | null
  isLoading: boolean
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  logout: () => void
}

export const useAuthStore = create<AuthStore>((set) => ({
  user: null,
  isLoading: true,

  setUser: (user) => set({ user }),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  logout: () => set({ user: null }),
}))
