/**
 * Product Image Utility
 * Centralized image mapping for consistent product image display across all components
 */

// Product image mapping based on actual files in public/img/
const PRODUCT_IMAGE_MAP: { [key: string]: string } = {
  // Exact product names (case-insensitive)
  'cipak koceak': '/img/cipak koceak.jpg',
  'cipak mozarella': '/img/cipak mozarella.jpg', 
  'cirambay': '/img/cirambay.jpg',
  'basreng': '/img/basreng.jpg',
  
  // Alternative spellings and variations
  'cipak mozzarella': '/img/cipak mozarella.jpg',
  'cipak mozzarela': '/img/cipak mozarella.jpg',
  'cipak mozarela': '/img/cipak mozarella.jpg',
  'cireumbay': '/img/cirambay.jpg',
  'cirembay': '/img/cirambay.jpg',
  
  // Fallback variations
  'koceak': '/img/cipak koceak.jpg',
  'mozarella': '/img/cipak mozarella.jpg',
  'mozzarella': '/img/cipak mozarella.jpg',
}

// Default fallback image
const DEFAULT_PRODUCT_IMAGE = '/img/cipak koceak.jpg'

/**
 * Get product image path based on product name
 * @param productName - Product name from database
 * @returns Image path for the product
 */
export function getProductImage(productName: string): string {
  if (!productName) {
    return DEFAULT_PRODUCT_IMAGE
  }

  // Normalize product name: lowercase, trim whitespace
  const normalizedName = productName.toLowerCase().trim()
  
  // Direct match
  if (PRODUCT_IMAGE_MAP[normalizedName]) {
    return PRODUCT_IMAGE_MAP[normalizedName]
  }
  
  // Partial match - check if product name contains any key
  for (const [key, imagePath] of Object.entries(PRODUCT_IMAGE_MAP)) {
    if (normalizedName.includes(key)) {
      return imagePath
    }
  }
  
  // Fallback to default
  return DEFAULT_PRODUCT_IMAGE
}

/**
 * Get all available product images
 * @returns Array of available image paths
 */
export function getAvailableProductImages(): string[] {
  return Array.from(new Set(Object.values(PRODUCT_IMAGE_MAP)))
}

/**
 * Validate if image exists (for development/debugging)
 * @param imagePath - Image path to validate
 * @returns Promise<boolean>
 */
export async function validateImageExists(imagePath: string): Promise<boolean> {
  try {
    const response = await fetch(imagePath, { method: 'HEAD' })
    return response.ok
  } catch {
    return false
  }
}

/**
 * Get product image with validation (development only)
 * @param productName - Product name
 * @returns Promise<string> - Validated image path
 */
export async function getProductImageWithValidation(productName: string): Promise<string> {
  const imagePath = getProductImage(productName)
  
  if (process.env.NODE_ENV === 'development') {
    const exists = await validateImageExists(imagePath)
    if (!exists) {
      console.warn(`Product image not found: ${imagePath} for product: ${productName}`)
    }
  }
  
  return imagePath
}

// Export for backward compatibility
export default getProductImage
