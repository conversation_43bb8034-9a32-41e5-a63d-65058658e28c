import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { v4 as uuidv4 } from 'uuid'

export interface UploadResult {
  success: boolean
  url?: string
  error?: string
}

export interface UploadOptions {
  maxSize?: number // in bytes
  allowedTypes?: string[]
  folder?: string
}

const DEFAULT_OPTIONS: UploadOptions = {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  folder: 'uploads'
}

/**
 * Upload file to local storage
 */
export async function uploadFile(
  file: File, 
  options: UploadOptions = {}
): Promise<UploadResult> {
  try {
    const opts = { ...DEFAULT_OPTIONS, ...options }

    // Validate file size
    if (file.size > opts.maxSize!) {
      return {
        success: false,
        error: `File terlalu besar. Maksimal ${formatFileSize(opts.maxSize!)}`
      }
    }

    // Validate file type
    if (!opts.allowedTypes!.includes(file.type)) {
      return {
        success: false,
        error: `Tipe file tidak didukung. Hanya ${opts.allowedTypes!.join(', ')}`
      }
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop()
    const fileName = `${uuidv4()}.${fileExtension}`
    
    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', opts.folder!)
    await mkdir(uploadDir, { recursive: true })

    // Save file
    const filePath = join(uploadDir, fileName)
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    await writeFile(filePath, buffer)

    // Return public URL
    const publicUrl = `/${opts.folder}/${fileName}`

    return {
      success: true,
      url: publicUrl
    }

  } catch (error) {
    console.error('Upload error:', error)
    return {
      success: false,
      error: 'Gagal mengupload file'
    }
  }
}

/**
 * Upload multiple files
 */
export async function uploadMultipleFiles(
  files: File[],
  options: UploadOptions = {}
): Promise<UploadResult[]> {
  const results = await Promise.all(
    files.map(file => uploadFile(file, options))
  )
  
  return results
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Validate image dimensions
 */
export async function validateImageDimensions(
  file: File,
  minWidth?: number,
  minHeight?: number,
  maxWidth?: number,
  maxHeight?: number
): Promise<{ valid: boolean; error?: string }> {
  return new Promise((resolve) => {
    const img = new Image()
    
    img.onload = () => {
      const { width, height } = img
      
      if (minWidth && width < minWidth) {
        resolve({ valid: false, error: `Lebar minimum ${minWidth}px` })
        return
      }
      
      if (minHeight && height < minHeight) {
        resolve({ valid: false, error: `Tinggi minimum ${minHeight}px` })
        return
      }
      
      if (maxWidth && width > maxWidth) {
        resolve({ valid: false, error: `Lebar maksimum ${maxWidth}px` })
        return
      }
      
      if (maxHeight && height > maxHeight) {
        resolve({ valid: false, error: `Tinggi maksimum ${maxHeight}px` })
        return
      }
      
      resolve({ valid: true })
    }
    
    img.onerror = () => {
      resolve({ valid: false, error: 'File bukan gambar yang valid' })
    }
    
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Compress image before upload
 */
export async function compressImage(
  file: File,
  maxWidth: number = 800,
  maxHeight: number = 600,
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    const img = new Image()
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
      }
      
      // Set canvas dimensions
      canvas.width = width
      canvas.height = height
      
      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height)
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            resolve(file) // Return original if compression fails
          }
        },
        file.type,
        quality
      )
    }
    
    img.onerror = () => {
      resolve(file) // Return original if loading fails
    }
    
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Generate thumbnail from image
 */
export async function generateThumbnail(
  file: File,
  size: number = 150
): Promise<File | null> {
  try {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    const img = new Image()
    
    return new Promise((resolve) => {
      img.onload = () => {
        // Set square canvas
        canvas.width = size
        canvas.height = size
        
        // Calculate crop dimensions for square thumbnail
        const { width, height } = img
        const minDimension = Math.min(width, height)
        const x = (width - minDimension) / 2
        const y = (height - minDimension) / 2
        
        // Draw cropped and resized image
        ctx.drawImage(
          img,
          x, y, minDimension, minDimension,
          0, 0, size, size
        )
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const thumbnailFile = new File([blob], `thumb_${file.name}`, {
                type: file.type,
                lastModified: Date.now()
              })
              resolve(thumbnailFile)
            } else {
              resolve(null)
            }
          },
          file.type,
          0.8
        )
      }
      
      img.onerror = () => resolve(null)
      img.src = URL.createObjectURL(file)
    })
  } catch (error) {
    console.error('Thumbnail generation error:', error)
    return null
  }
}
