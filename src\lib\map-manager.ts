/**
 * Unified Map Manager - Professional Solution
 * Handles all map instances across the application
 * Prevents container conflicts and ensures proper cleanup
 */

import { Map as LeafletMap, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Circle, Popup } from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface MapInstance {
  id: string
  map: LeafletMap
  container: HTMLElement
  created: number
}

class MapManager {
  private static instance: MapManager
  private maps: Map<string, MapInstance> = new Map()
  private isLeafletLoaded = false

  private constructor() {}

  static getInstance(): MapManager {
    if (!MapManager.instance) {
      MapManager.instance = new MapManager()
    }
    return MapManager.instance
  }

  async ensureLeafletLoaded(): Promise<void> {
    if (this.isLeafletLoaded) return

    // Dynamic import to ensure client-side only
    if (typeof window === 'undefined') return

    try {
      const L = await import('leaflet')

      // Fix default markers
      delete (L.Icon.Default.prototype as any)._getIconUrl
      L.Icon.Default.mergeOptions({
        iconRetinaUrl: '/leaflet/marker-icon-2x.png',
        iconUrl: '/leaflet/marker-icon.png',
        shadowUrl: '/leaflet/marker-shadow.png',
      })

      this.isLeafletLoaded = true
    } catch (error) {
      console.error('Failed to load Leaflet:', error)
      throw error
    }
  }

  async createMap(
    containerId: string,
    container: HTMLElement,
    options: {
      center: [number, number]
      zoom: number
      [key: string]: any
    }
  ): Promise<LeafletMap> {
    await this.ensureLeafletLoaded()

    // Destroy existing map if exists
    this.destroyMap(containerId)

    // Enhanced container cleanup
    if (container) {
      // Clear all content
      container.innerHTML = ''

      // Remove Leaflet internal properties
      delete (container as any)._leaflet_id
      delete (container as any)._leaflet
      delete (container as any)._leaflet_pos

      // Remove any CSS classes that might interfere
      container.className = container.className.replace(/leaflet-[^\s]*/g, '').trim()

      // Reset container styles
      container.style.position = 'relative'
    }

    // Wait for DOM to be ready and ensure cleanup is complete
    await new Promise(resolve => setTimeout(resolve, 150))

    // Validate container is still available
    if (!container || !container.parentNode) {
      throw new Error('Map container is no longer available in DOM')
    }

    try {
      // Create new map with enhanced error handling
      const L = await import('leaflet')

      // Check if container already has a map (double-check)
      if ((container as any)._leaflet_id) {
        console.warn('Container still has Leaflet ID, forcing cleanup...')
        delete (container as any)._leaflet_id
        await new Promise(resolve => setTimeout(resolve, 50))
      }

      const map = L.default.map(container, {
        center: options.center,
        zoom: options.zoom,
        zoomControl: true,
        attributionControl: true,
        preferCanvas: false, // Use SVG for better compatibility
        ...options
      })

      // Add default tile layer with error handling
      const tileLayer = L.default.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19,
        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==' // 1x1 transparent PNG
      })

      tileLayer.on('tileerror', (e: any) => {
        console.warn('Tile loading error:', e)
      })

      tileLayer.addTo(map)

      // Store map instance
      const mapInstance: MapInstance = {
        id: containerId,
        map,
        container,
        created: Date.now()
      }

      this.maps.set(containerId, mapInstance)

      return map
    } catch (error) {
      console.error('Failed to create map:', error)
      // Clean up on failure
      if (container) {
        container.innerHTML = ''
        delete (container as any)._leaflet_id
      }
      throw error
    }
  }

  destroyMap(containerId: string): void {
    const mapInstance = this.maps.get(containerId)
    if (mapInstance) {
      try {
        // Enhanced map cleanup
        const map = mapInstance.map

        // Remove all event listeners
        map.off()

        // Clear all layers
        map.eachLayer((layer: any) => {
          try {
            map.removeLayer(layer)
          } catch (e) {
            console.warn('Error removing layer:', e)
          }
        })

        // Remove the map
        map.remove()
      } catch (error) {
        console.warn(`Error removing map ${containerId}:`, error)
      }

      // Enhanced container cleanup
      if (mapInstance.container) {
        try {
          // Clear all content
          mapInstance.container.innerHTML = ''

          // Remove all Leaflet-related properties
          delete (mapInstance.container as any)._leaflet_id
          delete (mapInstance.container as any)._leaflet
          delete (mapInstance.container as any)._leaflet_pos

          // Remove Leaflet CSS classes
          if (mapInstance.container.className) {
            mapInstance.container.className = mapInstance.container.className
              .replace(/leaflet-[^\s]*/g, '')
              .trim()
          }

          // Reset styles
          mapInstance.container.style.cssText = ''
        } catch (error) {
          console.warn('Error cleaning container:', error)
        }
      }

      this.maps.delete(containerId)
    }
  }

  getMap(containerId: string): LeafletMap | null {
    const mapInstance = this.maps.get(containerId)
    return mapInstance ? mapInstance.map : null
  }

  destroyAllMaps(): void {
    for (const [containerId] of this.maps) {
      this.destroyMap(containerId)
    }
  }

  // Cleanup old maps (older than 5 minutes)
  cleanupOldMaps(): void {
    const now = Date.now()
    const maxAge = 5 * 60 * 1000 // 5 minutes

    for (const [containerId, mapInstance] of this.maps) {
      if (now - mapInstance.created > maxAge) {
        console.log(`Cleaning up old map: ${containerId}`)
        this.destroyMap(containerId)
      }
    }
  }

  // Get all active maps
  getActiveMaps(): string[] {
    return Array.from(this.maps.keys())
  }
}

// Export singleton instance
export const mapManager = MapManager.getInstance()

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    mapManager.destroyAllMaps()
  })

  // Periodic cleanup
  setInterval(() => {
    mapManager.cleanupOldMaps()
  }, 60000) // Every minute
}
