import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const bannerSchema = z.object({
  title: z.string().min(1, 'Judul banner wajib diisi'),
  subtitle: z.string().optional(),
  description: z.string().optional(),
  image: z.string().min(1, 'Gambar banner wajib diisi'),
  mobileImage: z.string().optional(),
  buttonText: z.string().optional(),
  buttonLink: z.string().optional(),
  position: z.enum(['HERO', 'PROMO', 'FOOTER']).default('HERO'),
  isActive: z.boolean().default(true),
  startDate: z.string().optional(),
  endDate: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const position = searchParams.get('position')
    const isActive = searchParams.get('isActive')

    const where: any = {}

    if (position) {
      where.position = position
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    const banners = await prisma.banner.findMany({
      where,
      orderBy: { order: 'asc' }
    })

    return NextResponse.json({
      success: true,
      data: banners
    })
  } catch (error) {
    console.error('Error fetching banners:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal memuat banner' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = bannerSchema.parse(body)

    // Get the next order number for this position
    const lastBanner = await prisma.banner.findFirst({
      where: { position: validatedData.position },
      orderBy: { order: 'desc' }
    })

    const nextOrder = lastBanner ? lastBanner.order + 1 : 1

    const banner = await prisma.banner.create({
      data: {
        ...validatedData,
        order: nextOrder,
        startDate: validatedData.startDate ? new Date(validatedData.startDate) : null,
        endDate: validatedData.endDate ? new Date(validatedData.endDate) : null
      }
    })

    return NextResponse.json({
      success: true,
      data: banner,
      message: 'Banner berhasil ditambahkan'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error('Error creating banner:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal menambahkan banner' },
      { status: 500 }
    )
  }
}
