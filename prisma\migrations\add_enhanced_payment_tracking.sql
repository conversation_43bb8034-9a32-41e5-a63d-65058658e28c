-- Migration: Add Enhanced Payment and COD Tracking Fields
-- Date: December 2024
-- Purpose: Support enhanced COD management and payment tracking features

-- Add EXPIRED status to PaymentStatus enum
ALTER TABLE `payments` MODIFY COLUMN `status` ENUM('PENDING', 'PAID', 'FAILED', 'REFUNDED', 'EXPIRED') NOT NULL DEFAULT 'PENDING';

-- Add enhanced COD tracking fields to payments table
ALTER TABLE `payments` 
ADD COLUMN `collectedBy` VARCHAR(191) NULL COMMENT 'Who collected the payment (DRIVER, ADMIN, etc.)',
ADD COLUMN `collectionNotes` TEXT NULL COMMENT 'Notes about payment collection',
ADD COLUMN `customerSignature` VARCHAR(191) NULL COMMENT 'Digital signature or confirmation',
ADD COLUMN `mockMode` BOOLEAN NULL DEFAULT FALSE COMMENT 'Track if payment was in mock mode';

-- Add enhanced COD delivery fields to deliveries table
ALTER TABLE `deliveries`
ADD COLUMN `status` VARCHAR(191) NULL DEFAULT 'PENDING' COMMENT 'PENDING, IN_TRANSIT, COMPLETED, FAILED',
ADD COLUMN `codCollected` BOOLEAN NULL DEFAULT FALSE COMMENT 'Track if COD was collected',
ADD COLUMN `codAmount` DECIMAL(10,2) NULL COMMENT 'Amount collected for COD',
ADD COLUMN `deliveryNotes` TEXT NULL COMMENT 'Driver notes about delivery',
ADD COLUMN `customerPresent` BOOLEAN NULL DEFAULT TRUE COMMENT 'Was customer present for delivery';

-- Create indexes for better query performance
CREATE INDEX `idx_payments_method_status` ON `payments` (`method`, `status`);
CREATE INDEX `idx_payments_created_at` ON `payments` (`createdAt`);
CREATE INDEX `idx_payments_collected_by` ON `payments` (`collectedBy`);
CREATE INDEX `idx_deliveries_status` ON `deliveries` (`status`);
CREATE INDEX `idx_deliveries_cod_collected` ON `deliveries` (`codCollected`);

-- Update existing COD payments to have proper tracking
UPDATE `payments` 
SET `collectedBy` = 'SYSTEM', 
    `collectionNotes` = 'Migrated from existing data',
    `mockMode` = FALSE
WHERE `method` = 'CASH_ON_DELIVERY' AND `status` = 'PAID' AND `collectedBy` IS NULL;

-- Update existing deliveries for COD orders
UPDATE `deliveries` d
JOIN `orders` o ON d.orderId = o.id
SET d.status = CASE 
    WHEN o.status = 'DELIVERED' THEN 'COMPLETED'
    WHEN o.status = 'OUT_FOR_DELIVERY' THEN 'IN_TRANSIT'
    ELSE 'PENDING'
END,
d.codCollected = CASE 
    WHEN o.paymentMethod = 'CASH_ON_DELIVERY' AND o.paymentStatus = 'PAID' THEN TRUE
    ELSE FALSE
END,
d.codAmount = CASE 
    WHEN o.paymentMethod = 'CASH_ON_DELIVERY' THEN o.total
    ELSE NULL
END
WHERE d.status IS NULL;

-- Add comments to tables for documentation
ALTER TABLE `payments` COMMENT = 'Enhanced payment records with COD tracking support';
ALTER TABLE `deliveries` COMMENT = 'Enhanced delivery tracking with COD collection support';

-- Create view for COD analytics
CREATE VIEW `cod_analytics` AS
SELECT 
    DATE(o.createdAt) as date,
    COUNT(*) as total_cod_orders,
    SUM(CASE WHEN o.paymentStatus = 'PAID' THEN 1 ELSE 0 END) as collected_orders,
    SUM(CASE WHEN o.paymentStatus = 'FAILED' THEN 1 ELSE 0 END) as failed_orders,
    SUM(CASE WHEN o.paymentStatus = 'PENDING' THEN 1 ELSE 0 END) as pending_orders,
    SUM(o.total) as total_amount,
    SUM(CASE WHEN o.paymentStatus = 'PAID' THEN o.total ELSE 0 END) as collected_amount,
    ROUND(
        (SUM(CASE WHEN o.paymentStatus = 'PAID' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2
    ) as collection_rate_percent
FROM `orders` o
WHERE o.paymentMethod = 'CASH_ON_DELIVERY'
GROUP BY DATE(o.createdAt)
ORDER BY date DESC;

-- Create view for payment history
CREATE VIEW `payment_history` AS
SELECT 
    p.id,
    p.paymentId,
    p.orderId,
    o.orderNumber,
    o.userId,
    u.name as customerName,
    u.email as customerEmail,
    p.amount,
    p.method,
    p.status,
    p.collectedBy,
    p.collectionNotes,
    p.mockMode,
    p.paidAt,
    p.createdAt,
    o.deliveryAddress,
    d.driverName,
    d.codCollected,
    d.deliveryNotes
FROM `payments` p
JOIN `orders` o ON p.orderId = o.id
JOIN `users` u ON o.userId = u.id
LEFT JOIN `deliveries` d ON o.id = d.orderId
ORDER BY p.createdAt DESC;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT ON cod_analytics TO 'app_user'@'%';
-- GRANT SELECT ON payment_history TO 'app_user'@'%';

-- Migration completed successfully
-- Next steps:
-- 1. Run: npx prisma db push (to sync schema)
-- 2. Run: npx prisma generate (to update client)
-- 3. Test COD management features
-- 4. Verify payment history functionality
