import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/prisma'
import { authOptions } from '@/lib/auth'
import { z } from 'zod'

const categorySchema = z.object({
  name: z.string().min(1, '<PERSON><PERSON> kategori wajib diisi'),
  description: z.string().optional(),
  image: z.string().optional(),
})

// GET /api/categories - Get all categories
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeProducts = searchParams.get('includeProducts') === 'true'

    const categories = await prisma.category.findMany({
      where: {
        isActive: true,
      },
      include: includeProducts ? {
        products: {
          where: {
            isActive: true,
          },
          take: 5, // Limit products per category
        },
      } : undefined,
      orderBy: {
        name: 'asc',
      },
    })

    return NextResponse.json({
      success: true,
      data: categories,
    })

  } catch (error) {
    console.error('Get categories error:', error)
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON><PERSON> k<PERSON> server' },
      { status: 500 }
    )
  }
}

// POST /api/categories - Create new category (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const data = categorySchema.parse(body)

    // Check if category name already exists
    const existingCategory = await prisma.category.findFirst({
      where: {
        name: data.name // MySQL is case-insensitive by default for VARCHAR
      }
    })

    if (existingCategory) {
      return NextResponse.json(
        { error: 'Kategori dengan nama ini sudah ada' },
        { status: 400 }
      )
    }

    const category = await prisma.category.create({
      data,
    })

    return NextResponse.json({
      success: true,
      message: 'Kategori berhasil dibuat',
      data: category,
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error('Create category error:', error)
    return NextResponse.json(
      { error: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
