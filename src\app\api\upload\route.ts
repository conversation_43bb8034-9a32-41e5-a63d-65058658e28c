import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { uploadFile, uploadMultipleFiles } from '@/lib/upload'

// POST /api/upload - Upload single or multiple files
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const folder = formData.get('folder') as string || 'uploads'
    const maxSize = parseInt(formData.get('maxSize') as string) || 5 * 1024 * 1024 // 5MB default

    if (files.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada file yang diupload' },
        { status: 400 }
      )
    }

    // Validate file count
    if (files.length > 10) {
      return NextResponse.json(
        { error: 'Maksimal 10 file per upload' },
        { status: 400 }
      )
    }

    const uploadOptions = {
      maxSize,
      folder,
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    }

    let results
    if (files.length === 1) {
      // Single file upload
      results = [await uploadFile(files[0], uploadOptions)]
    } else {
      // Multiple files upload
      results = await uploadMultipleFiles(files, uploadOptions)
    }

    // Check for any failures
    const failures = results.filter(result => !result.success)
    if (failures.length > 0) {
      return NextResponse.json(
        { 
          error: 'Beberapa file gagal diupload',
          details: failures.map(f => f.error),
          results
        },
        { status: 400 }
      )
    }

    // All successful
    const urls = results.map(result => result.url).filter(Boolean)

    return NextResponse.json({
      success: true,
      message: `${files.length} file berhasil diupload`,
      data: {
        urls,
        count: files.length
      }
    })

  } catch (error) {
    console.error('Upload API error:', error)
    return NextResponse.json(
      { error: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

// GET /api/upload - Get upload info/limits
export async function GET() {
  return NextResponse.json({
    success: true,
    data: {
      maxFileSize: 5 * 1024 * 1024, // 5MB
      maxFiles: 10,
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
      folders: ['products', 'avatars', 'uploads']
    }
  })
}
