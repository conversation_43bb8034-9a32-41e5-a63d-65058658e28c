'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>R<PERSON>, Flame, Clock, MapPin } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

export function Hero() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-red-600 via-orange-600 to-red-700">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
      </div>

      <div className="relative container mx-auto px-4 py-12 md:py-16 lg:py-24">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Content */}
          <div className="text-white space-y-6 md:space-y-8 z-10 order-2 lg:order-1">
            {/* Badge */}
            <div className="inline-flex items-center gap-1 md:gap-2 bg-yellow-400/90 backdrop-blur-sm text-red-800 px-4 md:px-6 py-2 md:py-3 rounded-full text-xs md:text-sm font-bold shadow-lg">
              <span className="text-sm md:text-lg">🌶️</span>
              <span className="hidden sm:inline">JAJANAN ACI TERPEDAS SE-JAKARTA!</span>
              <span className="sm:hidden">ACI TERPEDAS JAKARTA!</span>
              <span className="text-sm md:text-lg">🔥</span>
            </div>

            {/* Main Heading */}
            <div className="space-y-4 md:space-y-6">
              <h1 className="text-3xl md:text-4xl lg:text-6xl font-black leading-tight">
                <span className="block">ACIKOO</span>
                <span className="block bg-gradient-to-r from-yellow-300 via-yellow-400 to-orange-300 bg-clip-text text-transparent">
                  ACI PEDAS
                </span>
                <span className="block text-xl md:text-2xl lg:text-3xl font-bold text-orange-100 mt-1 md:mt-2">
                  yang Bikin Nagih!
                </span>
              </h1>

              <p className="text-base md:text-lg lg:text-xl text-orange-100 leading-relaxed max-w-xl">
                Rasain sensasi pedas autentik dengan cita rasa yang bikin nagih.
                <span className="text-yellow-300 font-bold block mt-1 md:mt-2">
                  Dijamin ketagihan! 🔥
                </span>
              </p>
            </div>

            {/* Features */}
            <div className="grid sm:grid-cols-3 gap-4">
              <div className="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
                  <span className="text-xl">🌶️</span>
                </div>
                <div>
                  <h3 className="font-bold text-yellow-300 text-sm">Bumbu Rahasia</h3>
                  <p className="text-xs text-orange-200">Resep Unggulan</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
                  <Flame className="w-5 h-5 text-red-800" />
                </div>
                <div>
                  <h3 className="font-bold text-yellow-300 text-sm">Pedas Nagih</h3>
                  <p className="text-xs text-orange-200">Level pedas pas</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
                  <Clock className="w-5 h-5 text-red-800" />
                </div>
                <div>
                  <h3 className="font-bold text-yellow-300 text-sm">Delivery Cepat</h3>
                  <p className="text-xs text-orange-200">30 menit sampai</p>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                className="bg-yellow-400 text-red-800 hover:bg-yellow-300 font-bold text-lg px-8 py-4 rounded-full shadow-2xl transform hover:scale-105 transition-all duration-300"
                asChild
              >
                <Link href="/products">
                  🛒 Pesan Sekarang
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-red-800 font-bold text-lg px-8 py-4 rounded-full backdrop-blur-sm bg-white/10"
                asChild
              >
                <a href="https://wa.me/6285282305679" target="_blank" rel="noopener noreferrer">
                  💬 Chat WhatsApp
                </a>
              </Button>
            </div>
          </div>

          {/* Right Content - Product Showcase */}
          <div className="relative order-1 lg:order-2">
            <div className="relative z-10 space-y-4 md:space-y-6">
              {/* Main Product Image */}
              <div className="relative bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-2xl md:rounded-3xl backdrop-blur-sm border border-yellow-400/30 p-3 md:p-6">
                <div className="relative aspect-square max-w-xs md:max-w-md mx-auto">
                  <div className="absolute inset-0 bg-white/90 rounded-xl md:rounded-2xl shadow-2xl"></div>
                  <div className="relative h-full p-2 md:p-4">
                    <Image
                      src="/img/cipak koceak.jpg"
                      alt="Cipak Koceak - Menu Signature Acikoo"
                      width={400}
                      height={400}
                      className="w-full h-full object-cover rounded-lg md:rounded-xl shadow-lg"
                    />
                  </div>
                </div>

                {/* Product Badge */}
                <div className="absolute top-2 md:top-4 left-2 md:left-4">
                  <span className="inline-block bg-red-600 text-white px-2 md:px-4 py-1 md:py-2 rounded-full text-xs md:text-sm font-bold shadow-lg">
                    🌶️ RECOMMENDED
                  </span>
                </div>
              </div>

              {/* Product Info Card - Hidden on mobile, shown on md+ */}
              <div className="hidden md:block bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-yellow-400/20">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Cipak Koceak</h3>
                <p className="text-gray-600 mb-4">
                  Aci pedas level maksimal dengan bumbu rahasia yang bikin nagih!
                </p>

                {/* Features */}
                <div className="flex flex-wrap gap-2">
                  <div className="flex items-center gap-1 bg-red-100 text-red-700 rounded-full px-3 py-1 text-sm">
                    <Flame className="w-4 h-4" />
                    <span>Extra Pedas</span>
                  </div>
                  <div className="flex items-center gap-1 bg-orange-100 text-orange-700 rounded-full px-3 py-1 text-sm">
                    <Clock className="w-4 h-4" />
                    <span>15 Menit</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-yellow-400/20 rounded-full blur-xl"></div>
            <div className="absolute -bottom-6 -left-6 w-20 h-20 bg-orange-400/20 rounded-full blur-xl"></div>
          </div>
        </div>
      </div>

      {/* Bottom transition */}
      <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-gray-50 to-transparent"></div>
    </section>
  )
}
