'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  ArrowLeft,
  MapPin,
  Clock,
  Phone,
  Mail,
  Navigation,
  Car,
  Bike,
  Truck,
  Instagram,
  Facebook,
  ExternalLink
} from 'lucide-react'
import { Navbar } from '@/components/layout/navbar'
import { Footer } from '@/components/layout/footer'
import { StoreLocationMap } from '@/components/maps/store-location-map'
import {
  STORE_CONFIG,
  getStoreLocation,
  getOperatingHours,
  formatOperatingHours,
  getContactInfo,
  getDeliveryZones,
  getSocialMedia,
  isStoreOpen
} from '@/lib/store-config'

export default function StoreLocationPage() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isOpen, setIsOpen] = useState(false)

  const storeLocation = getStoreLocation()
  const operatingHours = getOperatingHours()
  const contactInfo = getContactInfo()
  const deliveryZones = getDeliveryZones()
  const socialMedia = getSocialMedia()

  useEffect(() => {
    // Update time every minute
    const interval = setInterval(() => {
      setCurrentTime(new Date())
      setIsOpen(isStoreOpen())
    }, 60000)

    setIsOpen(isStoreOpen())

    return () => clearInterval(interval)
  }, [])

  const openGoogleMaps = () => {
    window.open(storeLocation.googleMapsUrl, '_blank')
  }

  const openWhatsApp = () => {
    const message = encodeURIComponent('Halo! Saya ingin bertanya tentang produk Acikoo.')
    window.open(`https://wa.me/${contactInfo.whatsapp.replace('+', '')}?text=${message}`, '_blank')
  }

  const getDayName = (dayKey: string) => {
    const days = {
      monday: 'Senin',
      tuesday: 'Selasa',
      wednesday: 'Rabu',
      thursday: 'Kamis',
      friday: 'Jumat',
      saturday: 'Sabtu',
      sunday: 'Minggu'
    }
    return days[dayKey as keyof typeof days] || dayKey
  }

  const getCurrentDay = () => {
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
    return days[currentTime.getDay()]
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Beranda
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Lokasi Toko</h1>
            <p className="text-gray-600">Informasi lengkap lokasi dan kontak {STORE_CONFIG.name}</p>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Store Information */}
          <div className="space-y-6">
            {/* Basic Info */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5 text-red-600" />
                    {STORE_CONFIG.name}
                  </CardTitle>
                  <Badge variant={isOpen ? 'default' : 'destructive'}>
                    {isOpen ? 'Buka' : 'Tutup'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Alamat</h3>
                  <p className="text-gray-600">{storeLocation.address}</p>
                  <p className="text-sm text-gray-500 mt-1">{storeLocation.landmark}</p>
                </div>

                <div className="flex gap-2">
                  <Button onClick={openGoogleMaps} className="flex-1">
                    <Navigation className="w-4 h-4 mr-2" />
                    Buka di Maps
                  </Button>
                  <Button variant="outline" onClick={openWhatsApp}>
                    <Phone className="w-4 h-4 mr-2" />
                    WhatsApp
                  </Button>
                </div>

                <div className="pt-4 border-t">
                  <p className="text-sm text-gray-600">
                    <strong>Koordinat:</strong> {storeLocation.latitude}, {storeLocation.longitude}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Operating Hours */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5 text-blue-600" />
                  Jam Operasional
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(operatingHours).map(([day, hours]) => {
                    const isToday = day === getCurrentDay()
                    return (
                      <div
                        key={day}
                        className={`flex justify-between items-center p-2 rounded ${
                          isToday ? 'bg-red-50 border border-red-200' : ''
                        }`}
                      >
                        <span className={`font-medium ${isToday ? 'text-red-700' : 'text-gray-700'}`}>
                          {getDayName(day)}
                          {isToday && <span className="ml-2 text-xs">(Hari ini)</span>}
                        </span>
                        <span className={`${isToday ? 'text-red-600 font-semibold' : 'text-gray-600'}`}>
                          {formatOperatingHours(hours)}
                        </span>
                      </div>
                    )
                  })}
                </div>

                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <Clock className="w-4 h-4 inline mr-1" />
                    Sekarang: {currentTime.toLocaleTimeString('id-ID', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })} - Toko {isOpen ? 'Buka' : 'Tutup'}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="w-5 h-5 text-green-600" />
                  Kontak
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <Phone className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-medium text-sm">WhatsApp</p>
                      <p className="text-sm text-gray-600">{contactInfo.whatsapp}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <Mail className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium text-sm">Email</p>
                      <p className="text-sm text-gray-600">{contactInfo.email}</p>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-semibold text-gray-900 mb-3">Media Sosial</h4>
                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(socialMedia.instagram.url, '_blank')}
                    >
                      <Instagram className="w-4 h-4 mr-2" />
                      Instagram
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(socialMedia.facebook.url, '_blank')}
                    >
                      <Facebook className="w-4 h-4 mr-2" />
                      Facebook
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Map and Delivery Info */}
          <div className="space-y-6">
            {/* Interactive Map */}
            <Card>
              <CardHeader>
                <CardTitle>Peta Lokasi</CardTitle>
              </CardHeader>
              <CardContent>
                <StoreLocationMap height="h-64" showDeliveryZones={false} />
                <div className="mt-4 flex gap-2">
                  <Button onClick={openGoogleMaps} size="sm" className="flex-1">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Buka di Google Maps
                  </Button>
                  <Button onClick={openWhatsApp} variant="outline" size="sm" className="flex-1">
                    <Phone className="w-4 h-4 mr-2" />
                    WhatsApp
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Delivery Zones */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="w-5 h-5 text-orange-600" />
                  Area Pengiriman
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-600">
                    Kami melayani pengiriman dalam radius 9km dari lokasi toko dengan tarif sebagai berikut:
                  </p>

                  {/* Interactive Delivery Zone Map */}
                  <StoreLocationMap height="h-80" showDeliveryZones={true} />

                  {deliveryZones.map((zone, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          index === 0 ? 'bg-green-500' :
                          index === 1 ? 'bg-yellow-500' : 'bg-orange-500'
                        }`}></div>
                        <div>
                          <p className="font-medium text-sm">{zone.name}</p>
                          <p className="text-xs text-gray-600">{zone.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-sm">Rp {zone.fee.toLocaleString('id-ID')}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-4 p-3 bg-green-50 rounded-lg">
                  <p className="text-sm text-green-800">
                    <Bike className="w-4 h-4 inline mr-1" />
                    <strong>Gratis Ongkir</strong> untuk pembelian ≥ Rp {STORE_CONFIG.delivery.freeDeliveryThreshold.toLocaleString('id-ID')} dalam radius 3km
                  </p>
                </div>

                <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <Car className="w-4 h-4 inline mr-1" />
                    Estimasi waktu pengiriman: 30-60 menit tergantung jarak
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Aksi Cepat</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  <Button variant="outline" asChild>
                    <Link href="/products">
                      Lihat Menu
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/cart">
                      Keranjang
                    </Link>
                  </Button>
                  <Button variant="outline" onClick={openWhatsApp}>
                    Chat Admin
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/orders">
                      Pesanan Saya
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
