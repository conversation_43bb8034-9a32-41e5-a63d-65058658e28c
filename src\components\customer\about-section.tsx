'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Flame, Heart, Users, Award, ChefHat, Clock } from 'lucide-react'
import Link from 'next/link'

export function AboutSection() {
  const menuItems = [
    {
      name: 'Cipak Koceak',
      price: 'Rp 10.000',
      description: 'Menu andalan yang bikin ketagihan! Aci kenyal dengan bumbu rahasia yang pedasnya pas banget.',
      emoji: '🌶️',
      isSignature: true,
    },
    {
      name: 'Cipak Mozarella',
      price: 'Rp 13.000',
      description: 'Level up dari Cipak Koceak! Ditambah keju mozarella yang meleleh di mulut.',
      emoji: '🧀',
      isPremium: true,
    },
    {
      name: 'Cirambay',
      price: 'Rp 12.000',
      description: 'Aci rambay berbentuk seperti kwetiau yang crispy di luar, kenyal di dalam! Bumbu pedasnya meresap sempurna.',
      emoji: '🔥',
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      price: 'Rp 13.000',
      description: 'Bakso goreng aci yang crispy abis! Dibalur bumbu pedas manis yang bikin nagih.',
      emoji: '⚡',
    },
  ]

  const features = [
    {
      icon: ChefHat,
      title: 'Resep Rahasia',
      description: 'Bumbu rahasia yang pasti bikin kamu ketagihan',
    },
    {
      icon: Flame,
      title: 'Level Pedas Pas',
      description: 'Pedasnya pas di lidah, bikin nagih tapi gak bikin sakit perut',
    },
    {
      icon: Heart,
      title: 'Dibuat dengan Cinta',
      description: 'Setiap porsi dibuat dengan penuh perhatian dan cinta',
    },
    {
      icon: Clock,
      title: 'Selalu Fresh',
      description: 'Dibuat fresh setiap hari dan menggunakan bahan-bahan berkualitas',
    },
  ]

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-orange-50 to-red-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-block mb-4">
            <Badge className="bg-red-100 text-red-800 px-4 py-2 text-sm font-semibold">
              🌶️ Tentang Kami
            </Badge>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Apa Sih <span className="text-red-600">Acikoo</span> Itu?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Acikoo adalah brand jajanan aci pedas yang lahir dari kecintaan terhadap cita rasa Indonesia.
            Kami menghadirkan jajanan berbahan dasar tepung aci dengan bumbu rahasia yang bikin nagih.
            <span className="text-red-600 font-semibold"> Setiap gigitan adalah perpaduan sempurna antara tekstur kenyal dan rasa pedas yang memanjakan lidah!</span>
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Menu Showcase */}
        <div className="bg-white rounded-3xl p-8 lg:p-12 shadow-2xl">
          <div className="text-center mb-12">
            <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              4 Menu Spesial yang Bikin <span className="text-red-600">Nagih!</span>
            </h3>
            <p className="text-lg text-gray-600">
              Setiap menu punya karakteristik unik yang bakal bikin kamu ketagihan
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {menuItems.map((item, index) => (
              <Card key={index} className="border-2 border-gray-100 hover:border-red-300 transition-all duration-300 transform hover:scale-105">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <span className="text-3xl">{item.emoji}</span>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="text-xl font-bold text-gray-900">{item.name}</h4>
                          {item.isSignature && (
                            <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                              ⭐ Signature
                            </Badge>
                          )}
                          {item.isPremium && (
                            <Badge className="bg-purple-100 text-purple-800 text-xs">
                              👑 Premium
                            </Badge>
                          )}
                        </div>
                        <p className="text-2xl font-bold text-red-600">{item.price}</p>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* CTA */}
          <div className="text-center mt-12">
            <p className="text-lg text-gray-600 mb-6">
              Penasaran sama rasanya? Yuk cobain sekarang!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white font-bold transform hover:scale-105 transition-all" asChild>
                <Link href="/products">
                  🌶️ Lihat Menu Lengkap
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8 mt-12 md:mt-16">
          <div className="text-center bg-white/50 backdrop-blur-sm rounded-xl p-4 md:p-6 shadow-lg border border-red-100">
            <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-red-600 mb-1 md:mb-2">1000+</div>
            <p className="text-gray-600 text-sm md:text-base">Pelanggan Puas</p>
          </div>
          <div className="text-center bg-white/50 backdrop-blur-sm rounded-xl p-4 md:p-6 shadow-lg border border-red-100">
            <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-red-600 mb-1 md:mb-2">4</div>
            <p className="text-gray-600 text-sm md:text-base">Menu Spesial</p>
          </div>
          <div className="text-center bg-white/50 backdrop-blur-sm rounded-xl p-4 md:p-6 shadow-lg border border-red-100">
            <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-red-600 mb-1 md:mb-2">100%</div>
            <p className="text-gray-600 text-sm md:text-base">Bahan Fresh</p>
          </div>
          <div className="text-center bg-white/50 backdrop-blur-sm rounded-xl p-4 md:p-6 shadow-lg border border-red-100">
            <div className="text-xl md:text-2xl lg:text-3xl font-bold text-red-600 mb-1 md:mb-2">⭐⭐⭐⭐⭐</div>
            <p className="text-gray-600 text-sm md:text-base">Rating Pelanggan</p>
          </div>
        </div>
      </div>
    </section>
  )
}
