# Acikoo - Modern E-commerce Platform

**Acikoo** adalah platform e-commerce modern yang dibangun dengan Next.js 15 dan teknologi terdepan. Platform ini menyediakan solusi lengkap untuk toko online dengan fokus pada kemudahan penggunaan, sistem pembayaran lokal (QRIS), dan manajemen lokasi pengiriman yang canggih.

## 🌟 Highlights

- **🎯 Complete E-commerce Solution**: Browse → Cart → Checkout → Payment → Tracking
- **🔧 Unified Admin Dashboard**: Ke<PERSON>la produk, pesanan, dan customer dalam satu interface
- **🗺️ Smart Location System**: OpenStreetMap integration dengan zona pengiriman otomatis
- **💳 Local Payment Support**: QRIS dan Cash on Delivery (COD)
- **📊 Real-time Analytics**: Dashboard dengan insights bisnis mendalam
- **📱 Mobile-first Design**: Responsive dan touch-friendly di semua device

## 🎯 About Acikoo

Acikoo adalah spesialis makanan ringan berbahan dasar aci dengan cita rasa pedas yang menggugah selera. Platform ini telah berkembang menjadi solusi e-commerce lengkap dengan sistem pembayaran modern, tracking pesanan real-time, dan analytics mendalam untuk mendukung pertumbuhan bisnis.

## 🚀 Fitur Utama

### 🛍️ Customer Experience
- **Product Catalog**: Browse produk dengan search, filter, dan pagination
- **Smart Cart**: Keranjang belanja dengan persistence dan real-time updates
- **Interactive Checkout**: Pemilihan lokasi dengan peta interaktif
- **Dual Payment System**:
  - **QRIS**: Upload bukti pembayaran untuk verifikasi
  - **COD**: Cash on Delivery dengan konfirmasi langsung
- **Order Tracking**: Real-time status pesanan dari pembayaran hingga pengiriman
- **Customer Support**: Sistem messaging terintegrasi dengan admin

### 👨‍💼 Admin Dashboard (Redesigned)
- **📊 Dashboard Home**:
  - Stat cards: Total Penjualan, Customer, Produk Terlaris
  - Grafik penjualan per produk dengan filter waktu (7d/30d/90d)
  - Tabel 5 pesanan terakhir dengan status real-time
- **📦 Product Management**: CRUD produk dengan upload gambar dan stock tracking
- **📋 Unified Orders (NEW)**:
  - Tab "Pesanan Masuk & Ongoing" dan "Riwayat Pesanan"
  - Enhanced filtering: customer, status, payment method, waktu
  - Complete workflow: verifikasi → kirim → selesai/batal
  - View bukti pembayaran dan detail lengkap
- **🚚 Delivery Analytics (NEW)**:
  - Frekuensi lokasi pengiriman dengan insights
  - Revenue analytics per zona delivery
  - Top delivery zones dan performance metrics
- **💬 Messages**: Komunikasi customer-admin yang efisien

### 🔧 Technical Features
- **Smart Location System**: OpenStreetMap + Leaflet dengan zona pengiriman otomatis
- **Real-time Updates**: Status pesanan dan notifikasi instant
- **Security**: Input validation, SQL injection prevention, role-based access
- **Performance**: Optimized images, lazy loading, server components

## 🛠️ Tech Stack

### Core Technologies
- **Framework**: Next.js 15 (App Router) dengan TypeScript
- **Frontend**: React 18, Server Components
- **Styling**: Tailwind CSS, Shadcn/ui Components
- **Database**: MySQL + Prisma ORM
- **Authentication**: NextAuth.js (JWT Strategy)

### Key Libraries & Tools
- **State Management**: Zustand (cart persistence)
- **Maps**: OpenStreetMap + Leaflet (no API costs)
- **Charts**: Recharts untuk analytics dashboard
- **Forms**: React Hook Form + Zod validation
- **Date**: date-fns untuk date manipulation
- **UI**: Radix UI components via Shadcn/ui
- **Icons**: Lucide React

### Infrastructure & Services
- **File Upload**: Cloudinary integration
- **Payment**: QRIS support dengan upload bukti
- **Security**: bcryptjs, input validation, SQL injection prevention
- **Performance**: Image optimization, lazy loading, caching

## 📦 Quick Start

### Prerequisites
- Node.js 18+
- MySQL database
- Cloudinary account (for image uploads)

### Installation

1. **Clone & Install**
   ```bash
   git clone [repository-url]
   cd acikoo
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env.local
   ```

   Configure your `.env.local`:
   ```env
   # Database
   DATABASE_URL="mysql://user:password@localhost:3306/acikoo"

   # Auth
   NEXTAUTH_SECRET="your-secret-key"
   NEXTAUTH_URL="http://localhost:3000"

   # Cloudinary
   CLOUDINARY_CLOUD_NAME="your-cloud-name"
   CLOUDINARY_API_KEY="your-api-key"
   CLOUDINARY_API_SECRET="your-api-secret"
   ```

3. **Database Setup**
   ```bash
   npx prisma generate
   npx prisma db push
   npx prisma db seed  # Optional: seed with sample data
   ```

4. **Run Development**
   ```bash
   npm run dev
   ```

   Visit `http://localhost:3000`

### Default Admin Access
- **Email**: <EMAIL>
- **Password**: admin123

## 📁 Project Structure

```
src/
├── app/                    # Next.js 14 App Router
│   ├── admin/             # Admin dashboard pages
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   └── customer/          # Customer pages
├── components/            # Reusable components
│   ├── ui/                # shadcn/ui components
│   ├── layout/            # Layout components
│   ├── customer/          # Customer-specific components
│   └── admin/             # Admin-specific components
├── lib/                   # Utilities and configurations
├── hooks/                 # Custom React hooks
├── types/                 # TypeScript definitions
├── store/                 # Zustand stores
└── prisma/                # Database schema and migrations
```

## 🗄 Database Schema & Models

### Core Entities
The database includes the following main tables with their relationships:

#### User Management
- **Users:** Customer dan admin accounts dengan role-based access (CUSTOMER/ADMIN)
- **Accounts:** OAuth account linking untuk social login
- **Sessions:** User session management
- **VerificationTokens:** Email verification tokens

#### Product Catalog
- **Categories:** Product categories dengan hierarchical structure
- **Products:** Product catalog dengan inventory tracking, pricing, dan image management

#### Communication System
- **Conversations:** Customer-admin communication threads
- **Messages:** Individual messages dalam conversations

#### Content Management
- **ContentSections:** Dynamic CMS functionality untuk website content

### Database Enums
```prisma
enum UserRole { CUSTOMER, ADMIN }
enum MessageStatus { UNREAD, READ, REPLIED }
```

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npx prisma studio` - Open Prisma Studio (database GUI)

### Adding New Components
```bash
# Add shadcn/ui components
npx shadcn@latest add [component-name]
```

## 🏗 Application Architecture

### State Management
Platform menggunakan React state dan NextAuth.js untuk session management:

#### Authentication
- NextAuth.js untuk user session management
- Role-based access control (CUSTOMER/ADMIN)
- Secure session handling dengan JWT

### Authentication System
NextAuth.js configuration dengan multiple providers:
- **Credentials Provider** untuk email/password authentication
- **Google Provider** (optional) untuk social login
- **JWT Strategy** untuk session management
- **Role-based callbacks** untuk authorization

### API Routes Structure
Comprehensive REST API dengan proper validation:

#### Product Management
- `GET /api/products` - Products dengan filtering, pagination, search
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/[id]` - Update product (admin only)
- `DELETE /api/products/[id]` - Delete product (admin only)

#### Admin Endpoints
- `/api/admin/dashboard/stats` - Dashboard analytics
- `/api/admin/customers` - Customer management
- `/api/admin/messages` - Message management

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Manual Deployment
1. Build the application: `npm run build`
2. Start the production server: `npm run start`

## 🔐 Environment Variables

### Required
- `DATABASE_URL` - MySQL connection string
- `NEXTAUTH_SECRET` - NextAuth.js secret
- `NEXTAUTH_URL` - Application URL

### Optional
- `GOOGLE_CLIENT_ID` - Google OAuth client ID
- `GOOGLE_CLIENT_SECRET` - Google OAuth client secret

Note: No external API keys required - using OpenStreetMap + Leaflet for location services

## 🎨 UI/UX Design System

### Design Philosophy
- **Mobile-first approach** dengan responsive design
- **Consistent component library** menggunakan shadcn/ui
- **Accessible design** dengan proper ARIA labels
- **Performance-focused** dengan optimized images dan lazy loading

### Color Scheme & Branding
- **Primary Colors:** Orange/Red gradient untuk brand identity Acikoo
- **Background:** Gradient dari orange-50 via white ke red-50
- **UI Components:** Neutral colors dengan orange accent
- **Dark/Light Theme:** Support dengan next-themes

### Component Architecture
```
components/
├── ui/                    # shadcn/ui base components
├── layout/               # Layout components (Navbar, Footer)
├── customer/             # Customer-facing components
├── admin/                # Admin dashboard components
├── maps/                 # OpenStreetMap + Leaflet integration
├── tracking/             # Order tracking components
└── upload/               # File upload components
```

## 🔐 Security & Validation

### Input Validation
- **Zod schemas** untuk semua API endpoints
- **Client-side validation** dengan React Hook Form
- **Server-side validation** untuk data integrity
- **SQL injection prevention** melalui Prisma ORM

### Authentication & Authorization
- **JWT-based sessions** dengan NextAuth.js
- **Role-based access control** (CUSTOMER/ADMIN)
- **Protected routes** untuk admin dashboard
- **Password hashing** dengan bcryptjs (12 rounds)

### Data Protection
- **Environment variables** untuk sensitive data
- **CORS configuration** untuk API security
- **Rate limiting** untuk API endpoints
- **File upload validation** dengan type dan size restrictions

## 📊 Business Logic & Features

### Store Configuration
```typescript
const STORE_CONFIG = {
  name: 'Acikoo',
  tagline: 'Aci Pedas Terenak!',
  location: {
    address: 'Jl. Tanah Merah No.15, Pluit, Jakarta Utara',
    latitude: -6.1275,
    longitude: 106.7906,
  }
}
```

### Location System
- **Store Location:** Interactive maps dengan OpenStreetMap + Leaflet
- **Address Display:** Store location visualization
- **Map Integration:** Leaflet maps untuk location services

## 📝 API Documentation

### Authentication Endpoints
- `POST /api/auth/signin` - User login dengan credentials
- `POST /api/auth/signup` - User registration dengan validation
- `POST /api/auth/signout` - User logout dan session cleanup

### Product Management
- `GET /api/products` - Products dengan filtering, search, pagination
- `GET /api/products/[id]` - Product detail dengan category info
- `POST /api/products` - Create product (admin only) dengan image upload
- `PUT /api/products/[id]` - Update product (admin only)
- `DELETE /api/products/[id]` - Soft delete product (admin only)

### Additional Endpoints
- `GET /api/categories` - Product categories
- `GET /api/messages/conversations` - Customer support chat
- `POST /api/upload` - File upload dengan validation

## 🧪 Testing

```bash
# Run tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.

## 📈 Performance & Scalability

### Optimizations
- **Server Components** untuk better performance dan SEO
- **Image Optimization** dengan Sharp dan Next.js Image component
- **Database Indexing** melalui Prisma untuk query optimization
- **Caching Strategies** untuk static content dan API responses
- **Lazy Loading** untuk components dan images

### Real-time Features
- **Real-time Chat System** antara customer dan admin
- **Live Inventory Updates** untuk stock management

### Scalability Considerations
- **Modular Architecture** untuk easy feature additions
- **API Rate Limiting** untuk prevent abuse
- **Database Connection Pooling** dengan Prisma
- **CDN Integration** untuk static assets
- **Horizontal Scaling** ready dengan stateless design

## 🔄 Changelog

### v3.0.0 (Current - December 2024) - ADMIN DASHBOARD REDESIGN
- 🎯 **Complete Admin Dashboard Rebuild**
  - **Dashboard Home**: New stat cards (Total Penjualan, Customer, Produk Terlaris)
  - **Product Sales Chart**: Interactive charts dengan filter waktu (7d/30d/90d)
  - **Recent Orders Table**: 5 pesanan terakhir dengan status real-time
  - **Unified Orders Menu**: Gabungan customer + orders dalam satu interface

- 🚚 **New Delivery Analytics Module**
  - **Frekuensi Lokasi**: Analytics pengiriman berdasarkan zona
  - **Revenue Insights**: Performance delivery per area
  - **Zone Analytics**: Top delivery zones dengan metrics

- 📋 **Enhanced Order Management**
  - **Tab Navigation**: "Pesanan Masuk & Ongoing" vs "Riwayat Pesanan"
  - **Advanced Filtering**: Customer, status, payment method, waktu
  - **Preserved Workflow**: Verifikasi → kirim → selesai/batal tetap lengkap
  - **Payment Proof**: View bukti pembayaran QRIS tetap berfungsi

- 🧹 **Navigation Cleanup**
  - **Removed**: Laporan, Konten, Pelanggan (standalone)
  - **Streamlined**: 5 menu utama (Dashboard, Produk, Pesanan, Analisis Pengiriman, Pesan)
  - **Future Ready**: Menu tersembunyi siap untuk future improvements

### v2.0.0 (Previous - November 2024) - CUSTOMER FLOW REBUILD
- ✅ **Complete Customer E-commerce Flow**
  - Shopping cart dengan Zustand persistence
  - Interactive checkout dengan OpenStreetMap
  - QRIS dan COD payment systems
  - Real-time order tracking
  - Customer messaging system

- ✅ **Core Features Established**
  - Product catalog dengan filtering dan search
  - User authentication dan authorization
  - Admin product management CRUD
  - Location services dengan delivery zones

## 🔮 Future Improvements

### 📊 Reports & Analytics (Hidden - Ready for Implementation)
- **Advanced Sales Reports**: Detailed revenue analytics
- **Customer Behavior Reports**: Purchase patterns dan insights
- **Product Performance Reports**: Best sellers dan inventory analytics
- **Export Functionality**: PDF dan Excel reports

### 🎨 Content Management (Hidden - Ready for Implementation)
- **Homepage Management**: Dynamic content editing
- **Banner Management**: Promotional banners dan campaigns
- **SEO Management**: Meta tags dan content optimization
- **Media Library**: Centralized asset management

### 🔔 Real-time Enhancements
- **WebSocket Integration**: Real-time notifications
- **Push Notifications**: Order updates dan promotions
- **Live Chat**: Enhanced customer support
- **Auto-refresh**: Real-time dashboard updates

### 🗺️ Advanced Location Features
- **Delivery Heatmap**: Visual delivery frequency mapping
- **Route Optimization**: Efficient delivery planning
- **Geofencing**: Location-based promotions
- **Multi-location Support**: Multiple store locations

### 📱 Mobile & PWA
- **Progressive Web App**: Offline support
- **Mobile App**: React Native implementation
- **Touch Optimizations**: Better mobile UX
- **App Store Deployment**: iOS dan Android apps

### 🔗 Third-party Integrations
- **Payment Gateways**: Midtrans, Xendit integration
- **Shipping APIs**: JNE, TIKI, Pos Indonesia
- **WhatsApp Business**: Automated customer communication
- **Social Media**: Instagram, Facebook integration

---

**Acikoo** - Empowering Indonesian E-commerce 🇮🇩

*Built with ❤️ using modern web technologies*
