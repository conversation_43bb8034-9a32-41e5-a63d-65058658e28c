import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Get active banners for homepage
    const heroBanners = await prisma.banner.findMany({
      where: {
        position: 'HERO',
        isActive: true,
        OR: [
          { startDate: null },
          { startDate: { lte: new Date() } }
        ],
        AND: [
          {
            OR: [
              { endDate: null },
              { endDate: { gte: new Date() } }
            ]
          }
        ]
      },
      orderBy: { order: 'asc' }
    })

    const promoBanners = await prisma.banner.findMany({
      where: {
        position: 'PROMO',
        isActive: true,
        OR: [
          { startDate: null },
          { startDate: { lte: new Date() } }
        ],
        AND: [
          {
            OR: [
              { endDate: null },
              { endDate: { gte: new Date() } }
            ]
          }
        ]
      },
      orderBy: { order: 'asc' }
    })

    // Get homepage content
    const homepageContent = await prisma.contentSection.findFirst({
      where: { 
        key: 'homepage',
        isActive: true 
      }
    })

    // Get featured products (top 6 active products)
    const featuredProducts = await prisma.product.findMany({
      where: { isActive: true },
      orderBy: [
        { isFeatured: 'desc' },
        { createdAt: 'desc' }
      ],
      take: 6,
      select: {
        id: true,
        name: true,
        description: true,
        price: true,
        image: true,
        category: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    let parsedContent = null
    if (homepageContent) {
      try {
        parsedContent = JSON.parse(homepageContent.content)
      } catch (error) {
        console.error('Error parsing homepage content:', error)
      }
    }

    const responseData = {
      heroBanners: heroBanners.map(banner => ({
        id: banner.id,
        title: banner.title,
        subtitle: banner.subtitle,
        description: banner.description,
        image: banner.image,
        mobileImage: banner.mobileImage,
        buttonText: banner.buttonText,
        buttonLink: banner.buttonLink
      })),
      promoBanners: promoBanners.map(banner => ({
        id: banner.id,
        title: banner.title,
        subtitle: banner.subtitle,
        description: banner.description,
        image: banner.image,
        mobileImage: banner.mobileImage,
        buttonText: banner.buttonText,
        buttonLink: banner.buttonLink
      })),
      featuredProducts,
      aboutSection: parsedContent?.aboutSection || {
        title: 'Tentang Acikoo',
        description: 'Aci pedas terenak di Jakarta Timur dengan resep rahasia turun temurun yang telah dipercaya ribuan pelanggan.',
        image: '/images/about-acikoo.jpg'
      },
      testimonials: parsedContent?.testimonials?.filter((t: any) => t.isActive) || []
    }

    return NextResponse.json({
      success: true,
      data: responseData
    })
  } catch (error) {
    console.error('Error fetching homepage content:', error)
    return NextResponse.json(
      { success: false, error: 'Gagal memuat konten homepage' },
      { status: 500 }
    )
  }
}
