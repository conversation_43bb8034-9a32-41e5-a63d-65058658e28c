'use client'

import { useEffect, useRef, useState } from 'react'
import { MapPin } from 'lucide-react'
import { loadLeafletAssets, STORE_LOCATION, DELIVERY_ZONES, TILE_LAYERS } from '@/lib/leaflet-maps'
import { formatCurrency } from '@/lib/utils'

interface StoreLocationMapProps {
  height?: string
  showDeliveryZones?: boolean
  className?: string
}

export function StoreLocationMap({
  height = 'h-64',
  showDeliveryZones = false,
  className = ''
}: StoreLocationMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    initializeMap()

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        // Remove all event listeners
        mapInstanceRef.current.off()

        // Remove all layers and markers
        mapInstanceRef.current.eachLayer((layer: any) => {
          mapInstanceRef.current.removeLayer(layer)
        })

        // Remove map instance
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [showDeliveryZones])

  const initializeMap = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Clean up existing map instance FIRST
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.off() // Remove all event listeners
          mapInstanceRef.current.remove() // Remove map instance
        } catch (e) {
          console.warn('Error cleaning up store map:', e)
        }
        mapInstanceRef.current = null
      }

      // Load Leaflet assets
      await loadLeafletAssets()

      if (!mapRef.current || !window.L) {
        setError('Map container tidak tersedia')
        return
      }

      // Clear and reset the map container completely
      mapRef.current.innerHTML = ''
      // Clear Leaflet internal ID (type assertion for internal property)
      ;(mapRef.current as any)._leaflet_id = null

      // Wait a bit to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 100))

      // Initialize map
      const map = window.L.map(mapRef.current, {
        center: [STORE_LOCATION.lat, STORE_LOCATION.lng],
        zoom: showDeliveryZones ? 12 : 15,
        zoomControl: true,
        attributionControl: true,
        scrollWheelZoom: true,
        dragging: true
      })

      // Add tile layer
      window.L.tileLayer(TILE_LAYERS.cartodb.url, {
        attribution: TILE_LAYERS.cartodb.attribution,
        maxZoom: 19
      }).addTo(map)

      // Add store marker
      const storeIcon = window.L.divIcon({
        html: `<div class="bg-red-600 text-white rounded-full w-10 h-10 flex items-center justify-center text-lg font-bold shadow-lg border-2 border-white">🏪</div>`,
        className: 'custom-div-icon',
        iconSize: [40, 40],
        iconAnchor: [20, 20]
      })

      window.L.marker([STORE_LOCATION.lat, STORE_LOCATION.lng], { icon: storeIcon })
        .addTo(map)
        .bindPopup(`
          <div class="text-center p-2">
            <strong class="text-lg">Acikoo Store</strong><br>
            <small class="text-gray-600">Jl. Tanah Merah No.15<br>Pluit, Jakarta Utara</small><br>
            <div class="mt-2">
              <a href="https://maps.google.com/?q=-6.1275,106.7906" target="_blank"
                 class="text-blue-600 hover:text-blue-800 text-sm">
                📍 Buka di Google Maps
              </a>
            </div>
          </div>
        `)
        .openPopup()

      // Add delivery zones if requested
      if (showDeliveryZones) {
        DELIVERY_ZONES.forEach((zone, index) => {
          const circle = window.L.circle([STORE_LOCATION.lat, STORE_LOCATION.lng], {
            color: zone.color,
            fillColor: zone.color,
            fillOpacity: zone.opacity,
            radius: zone.radius,
            weight: 2
          }).addTo(map)

          // Add zone label
          const zoneLabel = window.L.marker([
            STORE_LOCATION.lat + (index * 0.01),
            STORE_LOCATION.lng + (index * 0.01)
          ], {
            icon: window.L.divIcon({
              html: `<div class="bg-white px-2 py-1 rounded shadow text-xs font-bold border-2" style="border-color: ${zone.color}; color: ${zone.color}">
                Zone ${index + 1}<br/>
                <span class="text-green-600">${formatCurrency(zone.fee)}</span>
              </div>`,
              className: 'zone-label',
              iconSize: [80, 30],
              iconAnchor: [40, 15]
            })
          }).addTo(map)

          // Add popup to circle
          circle.bindPopup(`
            <div class="text-center">
              <strong>Zone ${index + 1}</strong><br>
              <small>Radius: ${zone.radius / 1000}km</small><br>
              <small>Biaya: ${formatCurrency(zone.fee)}</small>
            </div>
          `)
        })
      }

      mapInstanceRef.current = map
      setIsMapLoaded(true)
      setIsLoading(false)
    } catch (error) {
      console.error('Error initializing map:', error)
      setError('Gagal memuat peta')
      setIsLoading(false)
    }
  }

  if (error) {
    return (
      <div className={`${height} bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-center text-gray-500">
          <MapPin className="w-12 h-12 mx-auto mb-2" />
          <p className="font-medium">Gagal memuat peta</p>
          <p className="text-sm">Silakan refresh halaman</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${height} ${className}`}>
      <div
        ref={mapRef}
        className={`w-full ${height} rounded-lg border`}
        style={{ minHeight: '256px' }}
      />
      {isLoading && (
        <div className={`absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center`}>
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
            <p className="text-gray-600">Memuat peta...</p>
          </div>
        </div>
      )}
      {isMapLoaded && (
        <div className="absolute bottom-2 left-2 bg-white/90 px-2 py-1 rounded text-xs text-gray-600">
          {showDeliveryZones ? 'Klik zona untuk info detail' : 'Klik marker untuk info toko'}
        </div>
      )}
    </div>
  )
}
