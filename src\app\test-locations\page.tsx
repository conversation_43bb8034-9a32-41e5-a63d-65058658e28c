'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { MapPin, Search, CheckCircle, XCircle, Clock, Truck } from 'lucide-react'
import { LeafletAddressPicker } from '@/components/maps/leaflet-address-picker'
import { geocodeAddress, getDeliveryInfo, calculateDistance, STORE_LOCATION } from '@/lib/leaflet-maps'
import { JAKARTA_TEST_LOCATIONS, testSingleAddress } from '@/lib/test-jakarta-locations'
import { formatCurrency } from '@/lib/utils'

interface TestResult {
  address: string
  coordinates?: { lat: number; lng: number }
  distance?: number
  deliveryInfo?: any
  error?: string
  timestamp: Date
}

export default function TestLocationsPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [customAddress, setCustomAddress] = useState('')
  const [showAddressPicker, setShowAddressPicker] = useState(false)

  const runSingleTest = async (address: string) => {
    setIsLoading(true)
    
    try {
      const coordinates = await geocodeAddress(address)
      
      if (!coordinates) {
        const result: TestResult = {
          address,
          error: 'Address not found',
          timestamp: new Date()
        }
        setTestResults(prev => [result, ...prev])
        return
      }

      const distance = calculateDistance(STORE_LOCATION, coordinates)
      const deliveryInfo = getDeliveryInfo(coordinates)

      const result: TestResult = {
        address,
        coordinates,
        distance,
        deliveryInfo,
        timestamp: new Date()
      }

      setTestResults(prev => [result, ...prev])

    } catch (error) {
      const result: TestResult = {
        address,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      }
      setTestResults(prev => [result, ...prev])
    } finally {
      setIsLoading(false)
    }
  }

  const runAllTests = async () => {
    setIsLoading(true)
    setTestResults([])

    for (const location of JAKARTA_TEST_LOCATIONS) {
      await runSingleTest(location.address)
      // Small delay to avoid overwhelming the API
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    setIsLoading(false)
  }

  const handleCustomTest = () => {
    if (customAddress.trim()) {
      runSingleTest(customAddress.trim())
    }
  }

  const handleAddressSelect = (address: string, coordinates: any, deliveryInfo: any) => {
    const result: TestResult = {
      address,
      coordinates,
      distance: calculateDistance(STORE_LOCATION, coordinates),
      deliveryInfo,
      timestamp: new Date()
    }
    setTestResults(prev => [result, ...prev])
    setShowAddressPicker(false)
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-gray-900">
            🗺️ Test OpenStreetMap + Leaflet
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Test geocoding dan delivery range validation untuk berbagai lokasi di Jakarta
            menggunakan OpenStreetMap + Leaflet sebagai pengganti Google Maps.
          </p>
          <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
            <MapPin className="w-4 h-4" />
            <span>Store: Jl. Tanah Merah No.15, Pluit, Jakarta Utara</span>
            <span>({STORE_LOCATION.lat}, {STORE_LOCATION.lng})</span>
          </div>
        </div>

        {/* Test Controls */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Predefined Tests */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                Predefined Jakarta Locations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">
                Test {JAKARTA_TEST_LOCATIONS.length} lokasi yang sudah ditentukan di Jakarta
                untuk validasi akurasi geocoding dan delivery range.
              </p>
              <Button 
                onClick={runAllTests} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? 'Testing...' : `Test All ${JAKARTA_TEST_LOCATIONS.length} Locations`}
              </Button>
            </CardContent>
          </Card>

          {/* Custom Test */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="w-5 h-5 text-blue-600" />
                Custom Address Test
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder="Masukkan alamat Jakarta..."
                  value={customAddress}
                  onChange={(e) => setCustomAddress(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleCustomTest()}
                />
                <div className="flex gap-2">
                  <Button 
                    onClick={handleCustomTest} 
                    disabled={isLoading || !customAddress.trim()}
                    className="flex-1"
                  >
                    Test Address
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => setShowAddressPicker(true)}
                  >
                    <MapPin className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Address Picker Modal */}
        {showAddressPicker && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-auto">
              <div className="p-4 border-b flex justify-between items-center">
                <h3 className="text-lg font-semibold">Pick Location on Map</h3>
                <Button variant="ghost" onClick={() => setShowAddressPicker(false)}>
                  ✕
                </Button>
              </div>
              <div className="p-4">
                <LeafletAddressPicker
                  onAddressSelect={handleAddressSelect}
                />
              </div>
            </div>
          </div>
        )}

        {/* Results Controls */}
        {testResults.length > 0 && (
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">
              Test Results ({testResults.length})
            </h2>
            <Button variant="outline" onClick={clearResults}>
              Clear Results
            </Button>
          </div>
        )}

        {/* Test Results */}
        <div className="space-y-4">
          {testResults.map((result, index) => (
            <Card key={index} className="border-l-4 border-l-blue-500">
              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-gray-900">{result.address}</h3>
                      <p className="text-xs text-gray-500">
                        {result.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                    {result.error ? (
                      <Badge variant="destructive" className="flex items-center gap-1">
                        <XCircle className="w-3 h-3" />
                        Error
                      </Badge>
                    ) : (
                      <Badge variant="default" className="flex items-center gap-1">
                        <CheckCircle className="w-3 h-3" />
                        Success
                      </Badge>
                    )}
                  </div>

                  {/* Error */}
                  {result.error && (
                    <Alert variant="destructive">
                      <AlertDescription>{result.error}</AlertDescription>
                    </Alert>
                  )}

                  {/* Success Results */}
                  {!result.error && result.coordinates && result.deliveryInfo && (
                    <div className="grid md:grid-cols-2 gap-4">
                      {/* Location Info */}
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm text-gray-700">Location Info</h4>
                        <div className="text-sm space-y-1">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Coordinates:</span>
                            <span className="font-mono">
                              {result.coordinates.lat.toFixed(4)}, {result.coordinates.lng.toFixed(4)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Distance:</span>
                            <span>{result.distance?.toFixed(2)} km</span>
                          </div>
                        </div>
                      </div>

                      {/* Delivery Info */}
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm text-gray-700">Delivery Info</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600 text-sm">Status:</span>
                            <Badge variant={result.deliveryInfo.isDeliverable ? "default" : "destructive"}>
                              {result.deliveryInfo.isDeliverable ? (
                                <>
                                  <Truck className="w-3 h-3 mr-1" />
                                  Deliverable
                                </>
                              ) : (
                                <>
                                  <XCircle className="w-3 h-3 mr-1" />
                                  Out of Range
                                </>
                              )}
                            </Badge>
                          </div>
                          
                          {result.deliveryInfo.isDeliverable && (
                            <>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Fee:</span>
                                <span className="font-semibold text-green-600">
                                  {formatCurrency(result.deliveryInfo.fee)}
                                </span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Est. Time:</span>
                                <span className="flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {result.deliveryInfo.estimatedTime}
                                </span>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Testing locations...</p>
          </div>
        )}

        {/* Empty State */}
        {testResults.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No Test Results Yet
            </h3>
            <p className="text-gray-600">
              Run predefined tests or test a custom address to see results.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
