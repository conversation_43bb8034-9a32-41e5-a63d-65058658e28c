'use client'

import { useEffect, useRef, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Truck, MapPin, Home, Clock } from 'lucide-react'
import { OrderWithDetails } from '@/types'
import { STORE_LOCATION, formatCurrency } from '@/lib/delivery-pricing'
import { loadLeafletAssets } from '@/lib/leaflet-maps'

interface OrderTrackingMapProps {
  order: OrderWithDetails
}

// Komponen Map untuk Order Tracking menggunakan Native Leaflet API
function OrderTrackingDeliveryMap({
  storePosition,
  customerPosition,
  deliveryAddress,
  orderNumber
}: {
  storePosition: [number, number]
  customerPosition: [number, number]
  deliveryAddress: string
  orderNumber: string
}) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Initialize map menggunakan pattern dari store-location-map.tsx
  const initializeMap = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Clean up existing map instance FIRST (pattern dari store-location-map.tsx)
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.off() // Remove all event listeners
          mapInstanceRef.current.remove() // Remove map instance
        } catch (e) {
          console.warn('Error cleaning up delivery map:', e)
        }
        mapInstanceRef.current = null
      }

      // Load Leaflet assets
      await loadLeafletAssets()

      if (!mapRef.current || !window.L) {
        setError('Map container tidak tersedia')
        return
      }

      // Clear and reset the map container completely (pattern dari store-location-map.tsx)
      mapRef.current.innerHTML = ''
      // Clear Leaflet internal ID (key untuk menghindari "container already initialized")
      ;(mapRef.current as any)._leaflet_id = null

      // Wait a bit to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 100))

      // Calculate center point between store and customer
      const centerLat = (storePosition[0] + customerPosition[0]) / 2
      const centerLng = (storePosition[1] + customerPosition[1]) / 2

      // Initialize map
      const map = window.L.map(mapRef.current, {
        center: [centerLat, centerLng],
        zoom: 13,
        zoomControl: true,
        attributionControl: false,
        scrollWheelZoom: true,
        dragging: true
      })

      // Add tile layer
      window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
      }).addTo(map)

      // Store marker
      const storeIcon = window.L.divIcon({
        html: `<div style="background: #dc2626; color: white; border-radius: 50%; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; font-size: 16px; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.3); border: 3px solid white;">🏪</div>`,
        className: 'custom-store-icon',
        iconSize: [36, 36],
        iconAnchor: [18, 18]
      })

      window.L.marker(storePosition, { icon: storeIcon })
        .addTo(map)
        .bindPopup(`
          <div style="text-align: center;">
            <strong>🏪 Acikoo Store</strong><br />
            <span style="font-size: 12px; color: #666;">Titik Awal</span><br />
            ${STORE_LOCATION.address}
          </div>
        `)

      // Customer marker
      const customerIcon = window.L.divIcon({
        html: `<div style="background: #2563eb; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.3); border: 2px solid white;">📍</div>`,
        className: 'custom-customer-icon',
        iconSize: [32, 32],
        iconAnchor: [16, 16]
      })

      window.L.marker(customerPosition, { icon: customerIcon })
        .addTo(map)
        .bindPopup(`
          <div style="text-align: center;">
            <strong>📍 Lokasi Pengiriman</strong><br />
            <span style="font-size: 12px; color: #666;">Tujuan</span><br />
            ${deliveryAddress}
          </div>
        `)

      // Route line
      window.L.polyline([storePosition, customerPosition], {
        color: '#ef4444',
        weight: 3,
        opacity: 0.7,
        dashArray: '10, 10'
      }).addTo(map)

      mapInstanceRef.current = map
      setIsMapLoaded(true)
      setIsLoading(false)
    } catch (error) {
      console.error('Error initializing delivery map:', error)
      setError('Gagal memuat peta')
      setIsLoading(false)
    }
  }

  // Initialize map on mount
  useEffect(() => {
    initializeMap()

    // Cleanup on unmount
    return () => {
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.off()
          mapInstanceRef.current.remove()
        } catch (e) {
          console.warn('Error cleaning up map on unmount:', e)
        }
        mapInstanceRef.current = null
      }
    }
  }, []) // Empty dependency array - only run once

  if (error) {
    return (
      <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="text-center text-gray-500">
          <MapPin className="w-12 h-12 mx-auto mb-2" />
          <p className="font-medium">Gagal memuat peta</p>
          <p className="text-sm">Silakan refresh halaman</p>
        </div>
      </div>
    )
  }

  return (
    <div className="relative h-64">
      <div
        ref={mapRef}
        className="w-full h-64 rounded-lg border"
        style={{ minHeight: '256px' }}
      />
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
            <p className="text-gray-600">Memuat peta...</p>
          </div>
        </div>
      )}
    </div>
  )
}

export function OrderTrackingMap({ order }: OrderTrackingMapProps) {
  const [isClient, setIsClient] = useState(false)

  // Check if we're on client side
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Store position
  const storePosition: [number, number] = [STORE_LOCATION.lat, STORE_LOCATION.lng]

  // Customer position (from order)
  const customerPosition: [number, number] = order.deliveryLat && order.deliveryLng
    ? [order.deliveryLat, order.deliveryLng]
    : storePosition

  // Status configuration for tracking - KONSISTEN dengan orders page
  const statusMapping: { [key: string]: string } = {
    'PENDING_PAYMENT': 'PAYMENT_VERIFIED', // Map old status to new
    'PAYMENT_VERIFIED': 'PAYMENT_VERIFIED',
    'SHIPPED': 'SHIPPED',
    'DELIVERED': 'DELIVERED',
    'CANCELLED': 'CANCELLED'
  }

  const getTrackingStatus = () => {
    // Map status untuk konsistensi
    const rawStatus = order.status as string
    const mappedStatus = statusMapping[rawStatus] || rawStatus

    switch (mappedStatus) {
      case 'PAYMENT_VERIFIED':
        return {
          label: 'Menunggu Verifikasi',
          description: 'Pesanan sedang diverifikasi admin',
          icon: Clock,
          color: 'bg-blue-500',
          progress: 33
        }
      case 'SHIPPED':
        return {
          label: 'Sedang Dikirim',
          description: 'Pesanan dalam perjalanan ke lokasi Anda',
          icon: Truck,
          color: 'bg-orange-500',
          progress: 66
        }
      case 'DELIVERED':
        return {
          label: 'Selesai',
          description: 'Pesanan telah sampai di tujuan',
          icon: Home,
          color: 'bg-green-500',
          progress: 100
        }
      case 'CANCELLED':
        return {
          label: 'Dibatalkan',
          description: 'Pesanan dibatalkan',
          icon: Clock,
          color: 'bg-red-500',
          progress: 0
        }
      default:
        // Debug logging untuk development
        if (process.env.NODE_ENV === 'development') {
          console.log('Order tracking - Raw status:', rawStatus)
          console.log('Order tracking - Mapped status:', mappedStatus)
        }
        return {
          label: 'Menunggu Verifikasi',
          description: 'Pesanan sedang diverifikasi admin',
          icon: Clock,
          color: 'bg-blue-500',
          progress: 33
        }
    }
  }

  const trackingStatus = getTrackingStatus()
  const StatusIcon = trackingStatus.icon

  return (
    <div className="space-y-4">
      {/* Tracking Status Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center">
              <StatusIcon className="w-5 h-5 mr-2 text-gray-600" />
              Status Pengiriman
            </CardTitle>
            <Badge className={`${trackingStatus.color} text-white`}>
              {trackingStatus.label}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {/* Progress Bar */}
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{trackingStatus.progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-500 ${trackingStatus.color}`}
                style={{ width: `${trackingStatus.progress}%` }}
              />
            </div>
          </div>

          {/* Status Description */}
          <p className="text-sm text-gray-600 mb-4">
            {trackingStatus.description}
          </p>

          {/* Order Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Nomor Pesanan:</span>
              <p className="font-medium">{order.orderNumber}</p>
            </div>
            <div>
              <span className="text-gray-500">Total:</span>
              <p className="font-medium text-red-600">{formatCurrency(Number(order.total))}</p>
            </div>
            <div>
              <span className="text-gray-500">Metode Bayar:</span>
              <p className="font-medium">{order.paymentMethod === 'QRIS' ? '💳 QRIS' : '💰 COD'}</p>
            </div>
            <div>
              <span className="text-gray-500">Tanggal:</span>
              <p className="font-medium">
                {new Date(order.createdAt).toLocaleDateString('id-ID')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tracking Map */}
      {order.status === 'SHIPPED' && order.deliveryLat && order.deliveryLng && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <MapPin className="w-5 h-5 mr-2 text-red-600" />
              Peta Pengiriman
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isClient && (
              <OrderTrackingDeliveryMap
                storePosition={storePosition}
                customerPosition={customerPosition}
                deliveryAddress={order.deliveryAddress}
                orderNumber={order.orderNumber}
              />
            )}

            {/* Map Legend */}
            <div className="mt-3 flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Toko</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Tujuan</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-4 h-0.5 bg-red-500" style={{ borderStyle: 'dashed' }}></div>
                  <span>Rute</span>
                </div>
              </div>
              <span className="text-xs">
                📍 Real-time tracking
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delivery Address */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Home className="w-5 h-5 mr-2 text-green-600" />
            Alamat Pengiriman
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-700 leading-relaxed">
            {order.deliveryAddress}
          </p>
          {order.notes && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Catatan:</span>
              <p className="text-sm text-gray-600 mt-1">{order.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
