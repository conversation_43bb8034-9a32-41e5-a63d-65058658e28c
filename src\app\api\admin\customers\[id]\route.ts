import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/customers/[id] - Get customer detail
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: customerId } = await params

    // Get customer with orders
    const customer = await prisma.user.findUnique({
      where: {
        id: customerId,
        role: 'CUSTOMER'
      },
      include: {
        orders: {
          include: {
            orderItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            },
            payment: {
              select: {
                id: true,
                status: true,
                method: true,
                amount: true,
                verifiedAt: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json(
        { success: false, error: 'Customer not found' },
        { status: 404 }
      )
    }

    // Calculate order summary - use payment status from payment relation or order status
    const paidOrders = customer.orders.filter(order => {
      // Check payment status from payment relation or order status
      const paymentStatus = order.payment?.status
      const orderStatus = order.status

      return paymentStatus === 'PAID' ||
             orderStatus === 'PAYMENT_VERIFIED' ||
             orderStatus === 'SHIPPED' ||
             orderStatus === 'DELIVERED'
    })

    const totalSpent = paidOrders.reduce((sum, order) => sum + Number(order.total), 0)
    const totalOrders = customer.orders.length
    const averageOrderValue = totalOrders > 0 ? totalSpent / paidOrders.length : 0
    const lastOrderDate = customer.orders.length > 0 ? customer.orders[0].createdAt : null

    // Calculate favorite products
    const productStats: { [key: string]: { name: string, orderCount: number, totalSpent: number } } = {}

    customer.orders.forEach(order => {
      // Check if order is paid using same logic
      const paymentStatus = order.payment?.status
      const orderStatus = order.status
      const isPaid = paymentStatus === 'PAID' ||
                     orderStatus === 'PAYMENT_VERIFIED' ||
                     orderStatus === 'SHIPPED' ||
                     orderStatus === 'DELIVERED'

      if (isPaid) {
        order.orderItems.forEach(item => {
          const productId = item.product.id
          const productName = item.product.name

          if (!productStats[productId]) {
            productStats[productId] = {
              name: productName,
              orderCount: 0,
              totalSpent: 0
            }
          }

          productStats[productId].orderCount += item.quantity
          productStats[productId].totalSpent += Number(item.subtotal)
        })
      }
    })

    const favoriteProducts = Object.values(productStats)
      .sort((a, b) => b.orderCount - a.orderCount)
      .slice(0, 5)
      .map(product => ({
        productName: product.name,
        orderCount: product.orderCount,
        totalSpent: product.totalSpent
      }))

    const orderSummary = {
      totalOrders,
      totalSpent,
      averageOrderValue,
      lastOrderDate,
      favoriteProducts
    }

    // Format customer data
    const customerData = {
      id: customer.id,
      name: customer.name || customer.email,
      email: customer.email,
      phone: customer.phone,
      address: customer.address,
      latitude: customer.latitude,
      longitude: customer.longitude,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt
    }

    return NextResponse.json({
      success: true,
      data: {
        customer: customerData,
        orderSummary
      }
    })

  } catch (error) {
    console.error('Get customer detail error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/admin/customers/[id] - Update customer info
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id: customerId } = await params
    const body = await request.json()
    const { name, phone, address, notes } = body

    // Update customer
    const updatedCustomer = await prisma.user.update({
      where: {
        id: customerId,
        role: 'CUSTOMER'
      },
      data: {
        name,
        phone,
        address,
        // notes can be stored in a separate admin_notes field if needed
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedCustomer,
      message: 'Customer information updated successfully'
    })

  } catch (error) {
    console.error('Update customer error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
