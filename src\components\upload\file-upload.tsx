'use client'

import { useState, useRef, useCallback } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  File, 
  CheckCircle, 
  AlertTriangle,
  Loader2
} from 'lucide-react'
import { formatFileSize } from '@/lib/upload'
import Image from 'next/image'

interface FileUploadProps {
  onUploadComplete: (urls: string[]) => void
  maxFiles?: number
  maxSize?: number
  allowedTypes?: string[]
  folder?: string
  multiple?: boolean
  accept?: string
  className?: string
}

interface UploadFile {
  file: File
  id: string
  preview?: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
  url?: string
}

export function FileUpload({
  onUploadComplete,
  maxFiles = 5,
  maxSize = 5 * 1024 * 1024, // 5MB
  allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  folder = 'uploads',
  multiple = true,
  accept = 'image/*',
  className
}: FileUploadProps) {
  const [files, setFiles] = useState<UploadFile[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const generatePreview = (file: File): string | undefined => {
    if (file.type.startsWith('image/')) {
      return URL.createObjectURL(file)
    }
    return undefined
  }

  const validateFile = (file: File): string | null => {
    if (file.size > maxSize) {
      return `File terlalu besar. Maksimal ${formatFileSize(maxSize)}`
    }
    
    if (!allowedTypes.includes(file.type)) {
      return `Tipe file tidak didukung. Hanya ${allowedTypes.join(', ')}`
    }
    
    return null
  }

  const addFiles = useCallback((newFiles: FileList | File[]) => {
    const fileArray = Array.from(newFiles)
    
    // Check total file count
    if (files.length + fileArray.length > maxFiles) {
      alert(`Maksimal ${maxFiles} file`)
      return
    }

    const validFiles: UploadFile[] = []
    
    fileArray.forEach(file => {
      const error = validateFile(file)
      
      validFiles.push({
        file,
        id: Math.random().toString(36).substr(2, 9),
        preview: generatePreview(file),
        status: error ? 'error' : 'pending',
        progress: 0,
        error
      })
    })

    setFiles(prev => [...prev, ...validFiles])
  }, [files.length, maxFiles, maxSize, allowedTypes])

  const removeFile = (id: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === id)
      if (file?.preview) {
        URL.revokeObjectURL(file.preview)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  const uploadFiles = async () => {
    const pendingFiles = files.filter(f => f.status === 'pending')
    
    if (pendingFiles.length === 0) return

    setIsUploading(true)

    try {
      const formData = new FormData()
      
      pendingFiles.forEach(fileItem => {
        formData.append('files', fileItem.file)
      })
      
      formData.append('folder', folder)
      formData.append('maxSize', maxSize.toString())

      // Update status to uploading
      setFiles(prev => prev.map(f => 
        pendingFiles.some(pf => pf.id === f.id) 
          ? { ...f, status: 'uploading' as const, progress: 0 }
          : f
      ))

      // Simulate progress (in real implementation, you might use XMLHttpRequest for progress)
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(f => 
          f.status === 'uploading' 
            ? { ...f, progress: Math.min(f.progress + 10, 90) }
            : f
        ))
      }, 200)

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })

      clearInterval(progressInterval)

      const result = await response.json()

      if (response.ok && result.success) {
        // Update successful files
        setFiles(prev => prev.map((f, index) => {
          const pendingIndex = pendingFiles.findIndex(pf => pf.id === f.id)
          if (pendingIndex !== -1) {
            return {
              ...f,
              status: 'success' as const,
              progress: 100,
              url: result.data.urls[pendingIndex]
            }
          }
          return f
        }))

        // Call completion callback
        onUploadComplete(result.data.urls)

      } else {
        // Handle errors
        setFiles(prev => prev.map(f => 
          pendingFiles.some(pf => pf.id === f.id)
            ? { ...f, status: 'error' as const, error: result.error || 'Upload gagal' }
            : f
        ))
      }

    } catch (error) {
      console.error('Upload error:', error)
      setFiles(prev => prev.map(f => 
        pendingFiles.some(pf => pf.id === f.id)
          ? { ...f, status: 'error' as const, error: 'Terjadi kesalahan' }
          : f
      ))
    } finally {
      setIsUploading(false)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const droppedFiles = e.dataTransfer.files
    if (droppedFiles.length > 0) {
      addFiles(droppedFiles)
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
    if (selectedFiles && selectedFiles.length > 0) {
      addFiles(selectedFiles)
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-600" />
      default:
        return <File className="w-4 h-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: UploadFile['status']) => {
    const config = {
      pending: { label: 'Menunggu', variant: 'secondary' as const },
      uploading: { label: 'Uploading...', variant: 'default' as const },
      success: { label: 'Berhasil', variant: 'default' as const },
      error: { label: 'Gagal', variant: 'destructive' as const },
    }
    
    const { label, variant } = config[status]
    return <Badge variant={variant}>{label}</Badge>
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Drop Zone */}
      <Card 
        className={`border-2 border-dashed transition-colors ${
          isDragOver 
            ? 'border-red-400 bg-red-50' 
            : 'border-gray-300 hover:border-red-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <CardContent className="p-8 text-center">
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Upload File</h3>
          <p className="text-gray-600 mb-4">
            Drag & drop file di sini atau klik untuk memilih
          </p>
          <Button onClick={() => fileInputRef.current?.click()}>
            <ImageIcon className="w-4 h-4 mr-2" />
            Pilih File
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            multiple={multiple}
            onChange={handleFileSelect}
            className="hidden"
          />
          <div className="mt-4 text-xs text-gray-500">
            <p>Maksimal {maxFiles} file, {formatFileSize(maxSize)} per file</p>
            <p>Format: {allowedTypes.join(', ')}</p>
          </div>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-3">
              {files.map(fileItem => (
                <div key={fileItem.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  {/* Preview */}
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                    {fileItem.preview ? (
                      <Image
                        src={fileItem.preview}
                        alt={fileItem.file.name}
                        width={48}
                        height={48}
                        className="object-cover"
                      />
                    ) : (
                      <File className="w-6 h-6 text-gray-400" />
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{fileItem.file.name}</p>
                    <p className="text-sm text-gray-600">{formatFileSize(fileItem.file.size)}</p>
                    
                    {/* Progress */}
                    {fileItem.status === 'uploading' && (
                      <Progress value={fileItem.progress} className="mt-1 h-1" />
                    )}
                    
                    {/* Error */}
                    {fileItem.error && (
                      <p className="text-xs text-red-600 mt-1">{fileItem.error}</p>
                    )}
                  </div>

                  {/* Status */}
                  <div className="flex items-center gap-2">
                    {getStatusIcon(fileItem.status)}
                    {getStatusBadge(fileItem.status)}
                    
                    {/* Remove Button */}
                    {fileItem.status !== 'uploading' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(fileItem.id)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Upload Button */}
            {files.some(f => f.status === 'pending') && (
              <div className="mt-4 flex justify-end">
                <Button 
                  onClick={uploadFiles}
                  disabled={isUploading}
                  className="bg-red-600 hover:bg-red-700"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      Upload {files.filter(f => f.status === 'pending').length} File
                    </>
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
