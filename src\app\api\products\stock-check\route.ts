import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/prisma'
import { authOptions } from '@/lib/auth'
import { z } from 'zod'

const stockCheckSchema = z.object({
  items: z.array(z.object({
    productId: z.string(),
    quantity: z.number().int().positive(),
  })).min(1, 'Items array cannot be empty')
})

export interface StockWarning {
  productId: string
  productName: string
  requested: number
  available: number
  severity: 'warning' | 'error'
  message: string
  suggestion?: string
}

export interface StockCheckResponse {
  success: boolean
  warnings: StockWarning[]
  hasStockIssues: boolean
  canProceed: boolean
  message?: string
}

// POST /api/products/stock-check - Real-time stock validation
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const data = stockCheckSchema.parse(body)

    const warnings: StockWarning[] = []
    let canProceed = true

    // Check stock for each item
    for (const item of data.items) {
      const product = await prisma.product.findUnique({
        where: { 
          id: item.productId,
          isActive: true 
        },
        select: {
          id: true,
          name: true,
          stock: true,
          isActive: true
        }
      })

      if (!product) {
        warnings.push({
          productId: item.productId,
          productName: 'Produk tidak ditemukan',
          requested: item.quantity,
          available: 0,
          severity: 'error',
          message: 'Produk tidak tersedia atau telah dihapus',
          suggestion: 'Hapus item ini dari keranjang'
        })
        canProceed = false
        continue
      }

      // Check stock availability
      if (product.stock < item.quantity) {
        const severity = product.stock === 0 ? 'error' : 'warning'
        
        warnings.push({
          productId: item.productId,
          productName: product.name,
          requested: item.quantity,
          available: product.stock,
          severity,
          message: product.stock === 0 
            ? `${product.name} sudah habis`
            : `${product.name} hanya tersisa ${product.stock} item`,
          suggestion: product.stock === 0 
            ? 'Hapus item ini dari keranjang'
            : `Kurangi jumlah menjadi maksimal ${product.stock} item`
        })

        if (severity === 'error') {
          canProceed = false
        }
      }

      // Low stock warning (< 5 items)
      else if (product.stock < 5 && product.stock >= item.quantity) {
        warnings.push({
          productId: item.productId,
          productName: product.name,
          requested: item.quantity,
          available: product.stock,
          severity: 'warning',
          message: `${product.name} stok terbatas (${product.stock} tersisa)`,
          suggestion: 'Segera checkout sebelum kehabisan'
        })
      }
    }

    const response: StockCheckResponse = {
      success: true,
      warnings,
      hasStockIssues: warnings.length > 0,
      canProceed,
      message: canProceed 
        ? warnings.length > 0 
          ? 'Ada beberapa peringatan stok, tapi masih bisa melanjutkan'
          : 'Semua item tersedia'
        : 'Ada item yang tidak tersedia, silakan perbaiki sebelum melanjutkan'
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Stock check error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Data tidak valid', 
          details: error.errors 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Terjadi kesalahan server' 
      },
      { status: 500 }
    )
  }
}
