'use client'

import { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet'
import { LatLngExpression, DivIcon } from 'leaflet'
import { MapPin } from 'lucide-react'
import { STORE_LOCATION } from '@/lib/delivery-pricing'
import 'leaflet/dist/leaflet.css'

interface DeliveryMapProps {
  storePosition: LatLngExpression
  customerPosition: LatLngExpression
  deliveryAddress: string
  orderNumber: string
}

export function DeliveryMap({ 
  storePosition, 
  customerPosition, 
  deliveryAddress, 
  orderNumber 
}: DeliveryMapProps) {
  const [isMapReady, setIsMapReady] = useState(false)
  const mapRef = useRef<any>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Custom icons for markers
  const storeIcon = new DivIcon({
    html: `<div style="background: #dc2626; color: white; border-radius: 50%; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; font-size: 16px; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.3); border: 3px solid white;">🏪</div>`,
    className: 'custom-store-icon',
    iconSize: [36, 36],
    iconAnchor: [18, 18]
  })

  const customerIcon = new DivIcon({
    html: `<div style="background: #2563eb; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.3); border: 2px solid white;">📍</div>`,
    className: 'custom-customer-icon',
    iconSize: [32, 32],
    iconAnchor: [16, 16]
  })

  // Route line between store and customer
  const routeLine = [storePosition, customerPosition]

  // Calculate center point between store and customer
  const centerLat = (storePosition[0] + customerPosition[0]) / 2
  const centerLng = (storePosition[1] + customerPosition[1]) / 2
  const mapCenter: LatLngExpression = [centerLat, centerLng]

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = ''
      }
    }
  }, [])

  // Set map ready after mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsMapReady(true)
    }, 100)
    return () => clearTimeout(timer)
  }, [])

  if (!isMapReady) {
    return (
      <div className="h-64 rounded-lg overflow-hidden border bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <MapPin className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">Memuat peta...</p>
        </div>
      </div>
    )
  }

  return (
    <div ref={containerRef} className="h-64 rounded-lg overflow-hidden border">
      <MapContainer
        key={`delivery-map-${orderNumber}-${Date.now()}`}
        center={mapCenter}
        zoom={13}
        style={{ height: '100%', width: '100%' }}
        ref={mapRef}
        attributionControl={false}
        zoomControl={true}
        whenCreated={(mapInstance) => {
          // Ensure map is properly sized
          setTimeout(() => {
            mapInstance.invalidateSize()
          }, 100)
        }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Store Marker */}
        <Marker position={storePosition} icon={storeIcon}>
          <Popup>
            <div className="text-center">
              <strong>🏪 Acikoo Store</strong><br />
              <span className="text-sm text-gray-600">Titik Awal</span><br />
              {STORE_LOCATION.address}
            </div>
          </Popup>
        </Marker>

        {/* Customer Marker */}
        <Marker position={customerPosition} icon={customerIcon}>
          <Popup>
            <div className="text-center">
              <strong>📍 Lokasi Pengiriman</strong><br />
              <span className="text-sm text-gray-600">Tujuan</span><br />
              {deliveryAddress}
            </div>
          </Popup>
        </Marker>

        {/* Route Line */}
        <Polyline
          positions={routeLine}
          pathOptions={{
            color: '#ef4444',
            weight: 3,
            opacity: 0.7,
            dashArray: '10, 10'
          }}
        />
      </MapContainer>
    </div>
  )
}
